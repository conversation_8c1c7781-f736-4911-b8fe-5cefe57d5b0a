<?php
// if (isFeatureActive('start-page/dynamic') || isFeatureActive('campaigns/dynamic')) { // always inlcude the dynamic blocks because they are used in the Dynamic Block page template (page-dynamic-blocks.php)

// Add Dynamic Blocks Categories
function filter_block_categories_dynamic_start_page( $block_categories, $editor_context ) {
    if ( ! empty( $editor_context->post ) ) {
        array_unshift(
            $block_categories,
            [
                'slug'  => START_PAGE_DYNAMIC_PNP_BLOCK_CATEGORY_SLUG,
                'title' => __( 'Dynamic Blocks - PNP'),
                'icon'  => null,
            ]
        );
        array_unshift(
            $block_categories,
            [
                'slug'  => START_PAGE_DYNAMIC_BLOCK_CATEGORY_SLUG,
                'title' => __( 'Dynamic Blocks'),
                'icon'  => null,
            ]
        );
    }
    return $block_categories;
}
add_filter( 'block_categories_all', 'filter_block_categories_dynamic_start_page', 10, 2 );

// functions used for the dynamic blocks
require_once(get_template_directory() . "/dynamic/blocks/functions.php");

// Gutenberg Blocks includer
function register_dnyamic_blocks() {
    // register all blocks from the 'dynamic/blocks/' folder
    $blocks = glob(get_template_directory() . "/dynamic/blocks/*", GLOB_ONLYDIR);
    if (is_array($blocks)) {
        foreach ($blocks as $block) {
            register_block_type($block);
        }
    }
}
add_action('init', 'register_dnyamic_blocks');

// }
