<?php
/**
 * PnP Tracking AJAX Endpoint
 * Proxies tracking data and handles RegPixelTracking events
 */

// AJAX handler for PnP tracking
add_action('wp_ajax_pnp_tracking', 'handle_pnp_tracking');
add_action('wp_ajax_nopriv_pnp_tracking', 'handle_pnp_tracking');

function handle_pnp_tracking() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pnp_tracking_nonce')) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $action = isset($_POST['tracking_action']) ? $_POST['tracking_action'] : '';

    if ($action === 'get_base_data') {
        // Fetch base tracking data from the real endpoint
        $url = brandUrl() . '/tracking/?frame=wp';

        // Forward cookies to maintain session
        $cookies = [];
        foreach ($_COOKIE as $name => $value) {
            $cookies[] = $name . '=' . $value;
        }

        $response = wp_remote_get($url, [
            'timeout' => 2,
            'headers' => [
                // 'User-Agent' => 'WordPress/' . get_bloginfo('version') . '; ' . get_bloginfo('url'),
                'Cookie' => implode('; ', $cookies)
            ]
        ]);

        if (is_wp_error($response)) {
            wp_send_json_error('Failed to fetch tracking data');
            return;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['result']['eventInfo'])) {
            // Parse the eventInfo string (single quotes to double quotes)
            $eventInfo = str_replace("'", '"', $data['result']['eventInfo']);
            $eventData = json_decode($eventInfo, true);

            if ($eventData) {
                wp_send_json_success($eventData);
            } else {
                wp_send_json_error('Failed to parse event info');
            }
        } else {
            wp_send_json_error('No event info in response');
        }
    } else {
        // Handle tracking event submission
        $tracking_data = isset($_POST['tracking_data']) ? json_decode(stripslashes($_POST['tracking_data']), true) : [];

        if (empty($tracking_data)) {
            wp_send_json_error('No tracking data provided');
            return;
        }

        wp_send_json_success([
            'message' => 'Tracking event processed',
            'event' => $tracking_data['event'] ?? 'unknown'
        ]);
    }
}

// Add nonce for AJAX requests
add_action('wp_enqueue_scripts', 'add_pnp_tracking_nonce');
function add_pnp_tracking_nonce() {
    wp_localize_script('main', 'pnp_tracking', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('pnp_tracking_nonce')
    ]);
}