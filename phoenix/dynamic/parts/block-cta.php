<?php
global $blockButton;
$args = array_merge([
    'sticky' => false,
    'block-id' => '',
], $args);
$blockButtonClass = getClass([
    'btn',
    (!empty($blockButton['button_color']) ? 'btn--' . $blockButton['button_color'] : ''),
    (!empty($blockButton['button_size']) ? 'btn--' . $blockButton['button_size'] : ''),
    (!empty($blockButton['button_animation']) ? 'btn--animation-' . $blockButton['button_animation'] : ''),
    (!empty($blockButton['button_tracking']) ? 'js-' . $blockButton['button_tracking'] : 'js-button-claim'),
    ($args['sticky'] ? 'js-block-button-scroll' : ''),
]);
?>
<?php if (array_keys_have_values($blockButton, ['button_text', 'button_url'])) : ?>
    <div class="sp-block__button">
        <a href="<?= $blockButton['button_url']; ?>" class="<?= $blockButtonClass; ?>" data-content="<?= $args['block-id']; ?>">
            <?= $blockButton['button_text']; ?>
        </a>
    </div>

    <?php if ($args['sticky']) :
        ob_start();            ?>
        <div class="sp-block__button sp-block__button--sticky js-sticky-button" data-content="<?= $args['block-id']; ?>">
            <a href="<?= $blockButton['button_url']; ?>" class="<?= $blockButtonClass; ?> btn--full">
                <?= $blockButton['button_text']; ?>
            </a>
        </div>
<?php
        global $stickyButtonFooterElements;
        $stickyButtonFooterElements .= ob_get_clean();
    endif;
endif;
