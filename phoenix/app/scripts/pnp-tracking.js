/* global main_params, pnp_tracking, dataLayerHandler */

/**
 * PnP Tracking Module
 * Handles RegPixelTracking events for Pay and Play widgets
 */

const PnPTracking = (function() {
    'use strict';

    // Fetch base tracking data from WordPress AJAX endpoint
    async function fetchBaseTrackingData() {
        try {
            // Use WordPress AJAX endpoint
            if (typeof pnp_tracking !== 'undefined' && pnp_tracking.ajax_url) {
                const formData = new FormData();
                formData.append('action', 'pnp_tracking');
                formData.append('nonce', pnp_tracking.nonce);
                formData.append('tracking_action', 'get_base_data');

                const response = await fetch(pnp_tracking.ajax_url, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success && result.data) {
                    return result.data;
                }
            }
        } catch (error) {
            console.error('Failed to fetch tracking data:', error);
        }

        // Return default data if fetch fails
        return {
            pixel_country: main_params.country || "",
            pixel_language: main_params.language || "",
            pixel_affiliateId: "null",
            pixel_site: "0",
            pixel_device: window.innerWidth >= 768 ? "Desktop" : "Mobile",
            pixel_playerId: "",
            pixel_currentValue: "0",
            pixel_lastDeposit: "",
            pixel_leadProduct: "",
            pixel_currentProduct: "",
            pixel_highestValue: "",
            pixel_franchiseName: "DEFAULT",
            pixel_login: "not-logged-in",
            pixel_lastDepositDate: "",
            pixel_internal: "false",
            pixel_timestamp: "",
            pixel_cv: "",
            pixel_cwl: "",
            pixel_cwr: "",
            pixel_cw: ""
        };
    }

    // Get base tracking data that's common to all events
    async function getBaseTrackingData() {
        const baseData = await fetchBaseTrackingData();

        // Clone the base data and update required fields
        return {
            ...baseData,
            event: "RegPixelTracking"
        };
    }

    // Get current page context for tracking
    function getPageContext() {
        const bodyClasses = document.body.classList;

        if (bodyClasses.contains('start-page')) {
            return 'wp-start-page';
        } else if (bodyClasses.contains('dynamic')) {
            return 'wp-promo-page';
        }

        return 'wp-page';
    }

    // Get selected payment method
    function getSelectedPaymentMethod() {
        // For toggle layout
        const toggleInput = document.querySelector('input[name="payment-method"]:checked');
        if (toggleInput) {
            return toggleInput.getAttribute('data-method');
        }

        // For button layout, we'll need to track which was last clicked
        return window.lastSelectedPaymentMethod || 'unknown';
    }

    // Send tracking event to dataLayer
    function sendTrackingEvent(eventData) {
        // Push to dataLayer
        if (typeof dataLayerHandler === 'function') {
            dataLayerHandler(eventData);
        }
    }

    // Track amount pill click
    async function trackAmountPillClick(amount) {
        const baseData = await getBaseTrackingData();
        const trackingData = {
            ...baseData,
            pixel_reg_type: "pay-and-play",
            pixel_reg_step: getPageContext(),
            pixel_reg_action: `click-amount-suggested-${amount}`,
            pixel_payment_method: getSelectedPaymentMethod()
        };

        sendTrackingEvent(trackingData);
    }

    // Track payment method toggle
    async function trackPaymentMethodToggle(method) {
        const baseData = await getBaseTrackingData();
        const trackingData = {
            ...baseData,
            pixel_reg_type: "pay-and-play",
            pixel_reg_step: getPageContext(),
            pixel_reg_action: `click-method-toggle-${method}`,
            pixel_payment_method: method
        };

        sendTrackingEvent(trackingData);

        // Store for future reference
        window.lastSelectedPaymentMethod = method;
    }

    // Track manual amount entry
    async function trackAmountFieldClick() {
        const baseData = await getBaseTrackingData();
        const trackingData = {
            ...baseData,
            pixel_reg_type: "pay-and-play",
            pixel_reg_step: getPageContext(),
            pixel_reg_action: "click-amount-field",
            pixel_payment_method: getSelectedPaymentMethod()
        };

        sendTrackingEvent(trackingData);
    }

    // Track field validation
    async function trackFieldValidation(validationMessage) {
        const baseData = await getBaseTrackingData();
        const trackingData = {
            ...baseData,
            pixel_reg_type: "pay-and-play",
            pixel_reg_step: getPageContext(),
            pixel_reg_action: `amount-field-validation-${validationMessage}`,
            pixel_payment_method: getSelectedPaymentMethod()
        };

        sendTrackingEvent(trackingData);
    }

    // Track CTA click
    async function trackCTAClick(amount, isManual) {
        const baseData = await getBaseTrackingData();
        const trackingData = {
            ...baseData,
            pixel_reg_type: "pay-and-play",
            pixel_reg_step: getPageContext(),
            pixel_reg_action: `click-CTA-${isManual ? 'manual' : 'suggested'}-${amount}`,
            pixel_payment_method: getSelectedPaymentMethod()
        };

        sendTrackingEvent(trackingData);
    }

    // Public API
    return {
        trackAmountPillClick,
        trackPaymentMethodToggle,
        trackAmountFieldClick,
        trackFieldValidation,
        trackCTAClick
    };
})();

// Export for use in other modules
window.PnPTracking = PnPTracking;