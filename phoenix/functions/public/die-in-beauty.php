<?php
// Improved wp_die handler for better wordpress error pages
function die_in_beauty($message, $title, $args)
{
	if (empty($title)) {
		$title = 'Error';
	}

	if (function_exists('is_wp_error') && is_wp_error($message)) {
		$errors = $message->get_error_messages();
		switch (count($errors)) {
			case 0:
				$message = '';
				break;
			case 1:
				$message = $errors[0];
				break;
			default:
				$message = "<ul>\n\t\t<li>" . join("</li>\n\t\t<li>", $errors) . "</li>\n\t</ul>";
				break;
		}
	} else {
		$message = strip_tags($message);
	}
	get_header(); ?>

	<body class="<?= getBodyClass('article page'); ?>">
		<?php get_template_part('components/after-body'); ?>

		<?php get_template_part('components/header-layout'); ?>

		<div class="background">
			<article class="not-found-404">
				<h1><?php echo $title; ?></h1>
				<h3><?php echo apply_filters('the_content', $message); ?></h3>
			</article>
		</div>

		<script>
			setTimeout(function() {
				window.location.href = "<?= brandUrl(); ?>"
			}, 3000);
		</script>

	<?php get_footer(null, ['no-layout' => true]);
	die();
}

add_filter('wp_die_handler', 'die_in_beauty_filter');
function die_in_beauty_filter($die_handler)
{
	if (is_admin()) {
		return $die_handler;
	}
	return 'die_in_beauty';
}

function redirect_to_404() {
	wp_redirect(home_url('404'));
	exit();
}