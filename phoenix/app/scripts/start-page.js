/* Lint ------------------- */
/* global runAnimation, urlParamsFiltered, main_params, showStickyButtonEvent, hideStickyButtonEvent, capitalize, PnPTracking */

function synchronizeInputs(event) {
    const newValue = event.target.value; // Get the current value of the changed input

    // Select all inputs with the same name as the target input
    const inputs = document.querySelectorAll('.js-payment-form-amount, .js-payment-form--toggle-layout__amount');

    // Update all inputs with the same name to have the same value
    inputs.forEach(otherInput => {
        if (otherInput !== event.target) { // Avoid updating the input that triggered the event
            otherInput.value = newValue;
        }
    });
}

$(document).ready(function () {
    // Function to attach amount field focus handlers for PnP tracking
    function attachAmountFieldFocusHandlers() {
        // Amount field focus tracking only
        $(".js-payment-form-amount").off('focus.tracking').on('focus.tracking', function (e) {
            // Track amount field click
            if (typeof PnPTracking !== 'undefined') {
                PnPTracking.trackAmountFieldClick();
            }
        });

        // Track amount field click for toggle layout
        $(".js-payment-form--toggle-layout__amount").off('focus.tracking').on('focus.tracking', function (e) {
            // Track amount field click
            if (typeof PnPTracking !== 'undefined') {
                PnPTracking.trackAmountFieldClick();
            }
        });
    }

    // Pagination dots function
    function onScroll(event) {
        var scrollPos = $(document).scrollTop();
        var refElement;

        if($('.vertical-slider').length > 0) {
            $('.lp-slider .slider-nav li a').each(function () {
                var currLink = $(this);
                refElement = $(currLink.attr("data-href"));

                if (refElement.position().top - refElement.height() / 2 <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                    $('.slider-nav li a').removeClass("active");
                    currLink.addClass("active");
                    showStickyButtonEvent(refElement[0]);
                } else {
                    currLink.removeClass("active");
                }
            });

            if (scrollPos > $('.lp-slider .slides > div:last-child').position().top + $('.lp-slider .slides > div:last-child').height() / 2) {
                $('.slider-nav').fadeOut();
            } else {
                $('.slider-nav').fadeIn();
            }
        }

        if($('.pnp .payment-form').length > 0 ) { // show fixed forms when scrolled out of view the form
            refElement = $('.payment-form--normal');

            if (refElement.position().top + refElement.height() <= scrollPos) {
                $('.pnp').addClass('scrolled-fixed-payment-form');
            } else {
                $('.pnp').removeClass('scrolled-fixed-payment-form');
            }
        }
    }

    // Init payment
    function initPayment(methodName, methodId, amount) {
        var redirectTo = '/casino/explore';
        var queryParams;
        var methodParams = {
            amount: amount,
        };

        // Add type parameter only if payment method is not swish
        if (methodName.toLowerCase() !== "swish") {
            methodParams.type = 'guest-deposit-login';
            methodParams.methodName = capitalize(methodName);
        }
        // Else, add methodId parameter
        else {
            if (typeof methodId !== "undefined" && methodId !== null) {
                methodParams.methodId = Number(methodId);
            } else {
                methodParams.methodId = 235; // fallback if we can't get methodId via API
            }
        }

        switch(methodName) {
            case 'swish': {
                let swishParams = {
                    ...methodParams,
                    showMethods: true,
                    isSwishInstalled: false
                };
                queryParams = new URLSearchParams(swishParams).toString();
                redirectTo = '/guest-deposit-login';
                break;
            }

            default: {
                queryParams = new URLSearchParams(methodParams).toString();
            }
        }

        return `${redirectTo}?${queryParams}`;
    }

    // Check minimum amount
    function checkMinMaxVal(amountInput) {
        if (typeof amountInput == "undefined") return false;
        let amount = amountInput.val();
        let minAmount = amountInput.attr('min');
        let maxAmount = amountInput.attr('max');

        if (amount == '' || Number.isNaN(parseInt(amount)) || parseInt(amount) < parseInt(minAmount) || parseInt(amount) > parseInt(maxAmount)) {

            amountInput.focus();
            amountInput.val('');
            $('.payment-form__input').val('');

            let validationMessage = '';
            if (parseInt(amount) < parseInt(minAmount) || amount < 1 ) {
                validationMessage = `Min:${minAmount}${main_params.currency_symbol}`;
                amountInput.attr('placeholder', 'Min. ' + minAmount + ' ' + main_params.currency_symbol);
                $('.payment-form__input').attr('placeholder', 'Min. ' + minAmount + ' ' + main_params.currency_symbol);
            }

            if (parseInt(maxAmount) > 0) { // For backwards compatibility
                if (parseInt(amount) > parseInt(maxAmount) ) {
                    validationMessage = `Max:${maxAmount}${main_params.currency_symbol}`;
                    amountInput.attr('placeholder', 'Max. ' + maxAmount + ' ' + main_params.currency_symbol);
                    $('.payment-form__input').attr('placeholder', 'Max. ' + maxAmount + ' ' + main_params.currency_symbol);
                }
            }

            // Track field validation error
            if (validationMessage && typeof PnPTracking !== 'undefined') {
                PnPTracking.trackFieldValidation(validationMessage);
            }

            const amountInputs = document.querySelectorAll('.js-payment-form-amount, .payment-form__input--with-suffix:focus-within');
            amountInputs.forEach((amountInput) => runAnimation(amountInput, 'animation--wrong-input'));

            const depositLimitsText = document.querySelectorAll('.payment-form__deposit-limits span');
            depositLimitsText.forEach((limitText) => runAnimation(limitText, 'animation--red-text'));

            return false;
        }

        return true;
    }

    // Start Pages and Dynamic template only
    if($('body').hasClass('start-page') || $('body').hasClass('dynamic')) {
        // Pagination dots on scroll
        $(document).on("scroll", onScroll);
        onScroll();

        // Sliders
        $(".vertical-slider a[data-href]").on('click', function (e) {
            e.preventDefault();
            let thisElement = $(this);
            var targetSlide = $(thisElement.attr('data-href'));
            $([document.documentElement, document.body]).animate({
                scrollTop: $(thisElement.attr('data-href')).offset().top - $('.navigation').outerHeight() - ((targetSlide.is(':first-child'))?$('.casino-info-bar').outerHeight():0)
            }, 100);
            showStickyButtonEvent(targetSlide[0]);
        });
        if($(".horizontal-slider").length > 0) {
            var counter = $(".horizontal-slider .slides .slide-item").length;
            $.each($(".horizontal-slider .slides .slide-item"), function (index, el) {
                if (index !== 0) {
                    $(el).css({'display' : 'none'});
                }
            });
            $("[data-href]:first-child").addClass('active');
            $("[data-href]").on('click', function (e) {
                e.preventDefault();
                let thisElement = $('.slider-summary [data-href="' + $(this).attr('data-href') + '"]');
                if(!thisElement.hasClass('active')) {
                    $('[data-href]').removeClass("active");
                    thisElement.addClass("active");
                    var navigationDot = $('.slider-nav a[data-href="' + $(this).attr('data-href') + '"]');
                    navigationDot.addClass("active");

                    var targetSlide = $(thisElement.attr('data-href'));
                    $('.slide-item:not(' + thisElement.attr('data-href') + ')').stop().fadeOut(1000);

                    /* Before showing the next slider, we are expanding the slider container div according to next slider's height, to prevent jumping look issue,
                       and as a callback (which runs after updating .slides height) we show next slider with fadeIn animation */
                    $('.slides').delay(1000).animate({
                        height: targetSlide.height()
                    }, { duration: 500, complete: function() {
                        $(this).height('auto'); // Update height as auto, after using fixed height for animating on line 167 -> height: targetSlide.height()
                        targetSlide.fadeIn(1000);
                        showStickyButtonEvent(targetSlide[0]);
                    }});
                }
            });
        }

        // PNP Version
        if (
            $('body').hasClass('pnp') &&
            ($('.js-payment-form-amount').length || $('.js-payment-form--toggle-layout__amount').length)
        ) {

            $(".js-payment-form-amount-button").on('click', function(e) {
                const amountText = e.target.innerText;
                const amountValue = amountText.replace(main_params.currency_symbol, '').trim();

                // Temporarily remove focus event handlers
                $('.js-payment-form-amount').off('focus.tracking');
                $('.js-payment-form--toggle-layout__amount').off('focus.tracking');

                // Update values
                $('.js-payment-form-amount:not(.js-payment-form--toggle-layout__amount)').val(amountText);
                $('.js-payment-form--toggle-layout__amount').val(amountText.replace(main_params.currency_symbol, '').slice(0, -1));

                // Track amount pill click
                if (typeof PnPTracking !== 'undefined') {
                    PnPTracking.trackAmountPillClick(amountValue);
                }

                // Re-attach focus handlers after current execution cycle
                setTimeout(() => {
                    attachAmountFieldFocusHandlers();
                }, 0);
            });

            // Payment flow for Payment Method Buttons Layout
            $(".js-payment-form-button").on('click', function (e) {
                let closest_form = $(e.target).closest('.payment-form').get(0);
                let closest_input = $(closest_form).find('input').first();
                let methodName = e.target.getAttribute('data-method').toLowerCase();

                // Track payment method selection
                if (typeof PnPTracking !== 'undefined') {
                    window.lastSelectedPaymentMethod = methodName;
                }

                if (checkMinMaxVal(closest_input)) {
                    let amount     = parseInt(closest_input.val().replace(main_params.currency_symbol, ''));
                    let methodID   = e.target.getAttribute('data-method-id');
                    let urlParams  = urlParamsFiltered();

                    // Track CTA click - determine if manual or suggested
                    if (typeof PnPTracking !== 'undefined') {
                        const inputValue = closest_input.val().replace(main_params.currency_symbol, '').trim();
                        const isManual = !$('.js-payment-form-amount-button').toArray().some(btn =>
                            btn.innerText.replace(main_params.currency_symbol, '').trim() === inputValue
                        );
                        PnPTracking.trackCTAClick(amount, isManual);
                    }

                    window.location = main_params.brand_url + initPayment(methodName, methodID, amount) + (urlParams ? '&'+urlParams : '');
                } else {
                    return false;
                }
            });

            // Payment flow for Payment Method Toggle Layout
            $(".js-payment-form-single-button").on('click', function (e) {
                let payment_form = $(e.target).closest('.payment-form').get(0);
                let amount_input = $(payment_form).find('input[type="text"]').first();
                let payment_method = $(payment_form).find('input[name="payment-method"]:checked').get(0);
                if (checkMinMaxVal(amount_input)) {
                    let amount     = parseInt(amount_input.val().replace(main_params.currency_symbol, ''));
                    let methodName = payment_method.getAttribute('data-method').toLowerCase();
                    let methodID   = payment_method.getAttribute('data-method-id');
                    let urlParams  = urlParamsFiltered();

                    // Track CTA click - determine if manual or suggested
                    if (typeof PnPTracking !== 'undefined') {
                        const inputValue = amount_input.val().replace(main_params.currency_symbol, '').trim();
                        const isManual = !$('.js-payment-form-amount-button').toArray().some(btn =>
                            btn.innerText.replace(main_params.currency_symbol, '').trim() === inputValue
                        );
                        PnPTracking.trackCTAClick(amount, isManual);
                    }

                    window.location = main_params.brand_url + initPayment(methodName, methodID, amount) + (urlParams ? '&'+urlParams : '');
                } else {
                    return false;
                }
            });

            document.querySelectorAll('input[name="payment-method"]').forEach(input => {
                input.addEventListener('change', (event) => {
                    const selectedIndex = [...document.querySelectorAll('input[name="payment-method"]')]
                        .indexOf(event.target) % event.target.form.elements['payment-method'].length;

                    document.querySelectorAll('form').forEach(form => {
                        const options = form.querySelectorAll('input[name="payment-method"]');
                        if (options[selectedIndex]) {
                            options[selectedIndex].checked = true;
                        }
                    });

                    // Track payment method toggle
                    if (typeof PnPTracking !== 'undefined') {
                        const methodName = event.target.getAttribute('data-method').toLowerCase();
                        PnPTracking.trackPaymentMethodToggle(methodName);
                    }
                });
            });

            // Disable amount increment on mouse scroll
            $('input[type=number].js-payment-form-amount').on('mousewheel', function (e) { $(this).blur(); });
            $('input[type=number].js-payment-form--toggle-layout__amount').on('mousewheel', function (e) { $(this).blur(); });

            // Initial attachment of focus handlers
            attachAmountFieldFocusHandlers();

            // Keep the original focus handler for value formatting
            $(".js-payment-form-amount").on('focus', function (e) {
                e.currentTarget.value = e.currentTarget.value.replace(main_params.currency_symbol, '').slice(0, -1);
            });

            // $('.js-payment-form-amount').bind('keyup paste', function(e) {
            //     if (e.keyCode != 8 && e.keyCode != 46) {
            //         if(($(this).val() != 0) && ($(this).val().indexOf(main_params.currency_symbol) == -1)) {
            //             $('.js-payment-form-amount').val(parseInt($(this).val()) + main_params.currency_symbol);
            //         }
            //     }
            // });

            $(".js-payment-form-amount").focusout(function (e) {
                if(e.currentTarget.value != 0) {
                    $('.js-payment-form-amount').val(parseInt($(this).val().replace(/^0+(?=\d)/, '')) + ' ' + main_params.currency_symbol);
                } else {
                    $('.js-payment-form-amount').val("");
                }
            });


            $(".js-payment-form--toggle-layout__amount").focusout(function (e) {
                if(e.currentTarget.value != 0) {
                    $('.js-payment-form--toggle-layout__amount').val(parseInt($(this).val().replace(/^0+(?=\d)/, '')));
                } else {
                    $('.js-payment-form--toggle-layout__amount').val("");
                }
            });

            // Select all inputs with the specified name
            const inputs = document.querySelectorAll('.js-payment-form-amount, .js-payment-form--toggle-layout__amount');

            // List of events to listen for on each input
            const events = ["input", "change", "keydown", "keyup", "focusout", "paste"];

            // Add event listeners to each input for all specified events
            inputs.forEach(input => {
                events.forEach(eventType => {
                    input.addEventListener(eventType, synchronizeInputs);
                });
            });
        }
    }
});

