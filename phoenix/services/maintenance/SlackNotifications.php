<?php
// LOCAL_ENV constant can't be used here, since it's defined way after the inclusion of this file
if(!empty($_SERVER['SERVER_NAME']) && !preg_match('/localhost/', (string) $_SERVER['SERVER_NAME'])) {
    add_filter('wp_php_error_message', 'send_notification_to_slack', 10, 2);
}

function send_notification_to_slack($message, $error)
{
    $slack_notifications_endpoint      = '*********************************************************************************'; // Endpoint to #phoenix-errors channel only
    $last_notified_error_transient_key = 'px_slack_last_notified_error';
    $last_notified_error               = get_transient($last_notified_error_transient_key);
    $transient_storage_time_in_seconds = 1 * 3600; // 1 hour
    $current_time_in_seconds           = time();
    $current_error_message             = str_replace('Uncaught', '❌', (string) $error['message']);
    $current_error_message             = $current_error_message . ' at line ' . $error['line'];
    $notify_signal                     = false;

    $url_throwing_error = (empty($_SERVER['HTTPS']) ? 'http' : 'https') . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

    $post_id              = from_url_to_postid($url_throwing_error);
    if(!empty($post_id)) {
        $post                 = get_post($post_id);
        if(!empty($post)) {
            $post_author          = get_user_by('id', $post->post_author) ?? '-';
        }
    }
    $post_related_message = [];  // fallback;

    // Remove php stack trace from the error message
    $current_error_message = strstr($current_error_message, 'Stack trace', true) ?: $current_error_message;
    if (!empty($last_notified_error)) {
        if (is_array($last_notified_error)) {
            if (array_key_exists('message', $last_notified_error)) {
                if ($last_notified_error['message'] !== $current_error_message) {

                    $last_notified_error['message']        = $current_error_message;
                    $last_notified_error['notified_at']    = $current_time_in_seconds;

                    set_transient($last_notified_error_transient_key, $last_notified_error, $transient_storage_time_in_seconds);

                    $notify_signal = true;
                } else {
                    // Don't send notification for same error in 1 hour
                    if ($current_time_in_seconds > ($last_notified_error['notified_at'] + $transient_storage_time_in_seconds)) {
                        $last_notified_error['message']        = $current_error_message;
                        $last_notified_error['notified_at']    = $current_time_in_seconds;
                        set_transient($last_notified_error_transient_key, $last_notified_error, $transient_storage_time_in_seconds);
                        $notify_signal = true;
                    }
                }
            }
        }
    } else {
        $last_notified_error = [
            'message' => $current_error_message,
            'notified_at' => $current_time_in_seconds
        ];
        set_transient($last_notified_error_transient_key, $last_notified_error, $transient_storage_time_in_seconds);
    }

    if ($notify_signal) {

        $user_id = get_current_user_id();
        if ( is_user_logged_in() ) {
            $user = get_userdata( $user_id );
            $display_name = sanitize_text_field( $user->display_name );
        } else {
            $display_name = 'Visitor';
        }

        $error_triggered_by = '🔎 Error triggered by ('.$display_name.') on '.$_SERVER[ 'REQUEST_URI' ];

        // If page is not a dashboard page, then get post details
        if(!str_contains($url_throwing_error, '/wp-admin/')) {
            if(!empty($post_id) && !empty($post)) {
                $post_related_message = [
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Date:* " . $post->post_date
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Modified:* " . $post->post_modified
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Type:* " . $post->post_type
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post ID:* " . $post->ID
                    ],
                    [
                        "type" => "mrkdwn",
                        "text" => "*Post Author:* " . ($post_author->display_name ?: '')
                    ]
                ];
            }
        }

        $notification_body = [
            "blocks" => [
                [
                    "type" => "section",
                    "fields" => [
                        [
                            "type" => "mrkdwn",
                            "text" => "*Domain:* " . get_bloginfo('url')
                        ],
                        [
                            "type" => "mrkdwn",
                            "text" => "*WP Version:* " . get_bloginfo('version')
                        ],
                        [
                            "type" => "mrkdwn",
                            "text" => "*Release Version:* " . RELEASE_VERSION
                        ],
                        [
                            "type" => "mrkdwn",
                            "text" => "*PHP Version:* " . phpversion()
                        ]
                    ]
                ],
                [
                    "type" => "section",
                    "fields" => array_merge(
                        [
                            [
                                "type" => "mrkdwn",
                                "text" => $current_error_message
                            ],
                            [
                                "type" => "mrkdwn",
                                "text" => $error_triggered_by
                            ],
                        ],
                        $post_related_message
                    )
                ],
                [
                    "type" => "divider",
                ],
            ]
        ];

        $notification_body = wp_json_encode($notification_body);

        $options = [
            'body'        => $notification_body,
            'headers'     => [
                'Content-Type' => 'application/json',
            ],
            'timeout'     => 30,
            'httpversion' => '1.0',
            'sslverify'   => false,
            'data_format' => 'body',
        ];

        wp_remote_post($slack_notifications_endpoint, $options);
    }

    return $message;
}

/**
 * Improved version of WordPress's core function 'url_to_postid()'
 * which supports custom post types
 *
 * Examines a URL and try to determine the post ID it represents.
 *
 * Checks are supposedly from the hosted site blog.
 *
 * @since 1.0.0
 *
 * @global WP_Rewrite $wp_rewrite WordPress rewrite component.
 * @global WP         $wp         Current WordPress environment instance.
 *
 * @param string $url Permalink to check.
 * @return int Post ID, or 0 on failure.
 */
function from_url_to_postid( $url ) {
	global $wp_rewrite;

	/**
	 * Filters the URL to derive the post ID from.
	 *
	 * @since 2.2.0
	 *
	 * @param string $url The URL to derive the post ID from.
	 */
	$url = apply_filters( 'from_url_to_postid', $url );

	$url_host = parse_url( (string) $url, PHP_URL_HOST );

	if ( is_string( $url_host ) ) {
		$url_host = str_replace( 'www.', '', $url_host );
	} else {
		$url_host = '';
	}

	$home_url_host = parse_url( home_url(), PHP_URL_HOST );

	if ( is_string( $home_url_host ) ) {
		$home_url_host = str_replace( 'www.', '', $home_url_host );
	} else {
		$home_url_host = '';
	}


	// Bail early if the URL does not belong to this site.
	if ( $url_host && $url_host !== $home_url_host ) {
		return 0;
	}

	// First, check to see if there is a 'p=N' or 'page_id=N' to match against.
	if ( preg_match( '#[?&](p|page_id|attachment_id)=(\d+)#', (string) $url, $values ) ) {
		$id = absint( $values[2] );
		if ( $id ) {
			return $id;
		}
	}

	// Get rid of the #anchor.
	$url_split = explode( '#', (string) $url );
	$url       = $url_split[0];

	// Get rid of URL ?query=string.
	$url_split = explode( '?', $url );
	$url       = $url_split[0];

	// Set the correct URL scheme.
	$scheme = parse_url( home_url(), PHP_URL_SCHEME );
	$url    = set_url_scheme( $url, $scheme );

	// Add 'www.' if it is absent and should be there.
	if ( str_contains( home_url(), '://www.' ) && ! str_contains( $url, '://www.' ) ) {
		$url = str_replace( '://', '://www.', $url );
	}

	// Strip 'www.' if it is present and shouldn't be.
	if ( ! str_contains( home_url(), '://www.' ) ) {
		$url = str_replace( '://www.', '://', $url );
	}

	if ( trim( $url, '/' ) === home_url() && 'page' === get_option( 'show_on_front' ) ) {
		$page_on_front = get_option( 'page_on_front' );

		if ( $page_on_front && get_post( $page_on_front ) instanceof WP_Post ) {
			return (int) $page_on_front;
		}
	}

	// Check to see if we are using rewrite rules.
	$rewrite = $wp_rewrite->wp_rewrite_rules();

	// Not using rewrite rules, and 'p=N' and 'page_id=N' methods failed, so we're out of options.
	if ( empty( $rewrite ) ) {
		return 0;
	}

	// Strip 'index.php/' if we're not using path info permalinks.
	if ( ! $wp_rewrite->using_index_permalinks() ) {
		$url = str_replace( $wp_rewrite->index . '/', '', $url );
	}

	if ( str_contains( trailingslashit( $url ), home_url( '/' ) ) ) {
		// Chop off http://domain.com/[path].
		$url = str_replace( home_url(), '', $url );
	} else {
		// Chop off /path/to/blog.
		$home_path = parse_url( home_url( '/' ) );
		$home_path = $home_path['path'] ?? '';
		$url       = preg_replace( sprintf( '#^%s#', preg_quote( $home_path ) ), '', trailingslashit( $url ) );
	}

	// Trim leading and lagging slashes.
	$url = trim( (string) $url, '/' );

	$request              = $url;
	$post_type_query_vars = [];

	foreach ( get_post_types( [], 'objects' ) as $post_type => $t ) {
		if ( ! empty( $t->query_var ) ) {
			$post_type_query_vars[ $t->query_var ] = $post_type;
		}
	}

	// Look for matches.
	$request_match = $request;
	foreach ( (array) $rewrite as $match => $query ) {

		/*
		 * If the requesting file is the anchor of the match,
		 * prepend it to the path info.
		 */
		if ( ! empty( $url ) && ( $url !== $request ) && str_starts_with( $match, $url ) ) {
			$request_match = $url . '/' . $request;
		}

		if ( preg_match( "#^$match#", $request_match, $matches ) ) {

			if ( $wp_rewrite->use_verbose_page_rules && preg_match( '/pagename=\$matches\[([0-9]+)\]/', (string) $query, $varmatch ) ) {
				// This is a verbose page match, let's check to be sure about it.
				$page = get_page_by_path( $matches[ $varmatch[1] ] );
				if ( ! $page ) {
					continue;
				}

				$post_status_obj = get_post_status_object( $page->post_status );
				if ( ! $post_status_obj->public && ! $post_status_obj->protected
					&& ! $post_status_obj->private && $post_status_obj->exclude_from_search ) {
					continue;
				}
			}

			/*
			 * Got a match.
			 * Trim the query of everything up to the '?'.
			 */
			$query = preg_replace( '!^.+\?!', '', (string) $query );

			// Substitute the substring matches into the query.
			$query = addslashes( WP_MatchesMapRegex::apply( $query, $matches ) );

			// Filter out non-public query vars.
			global $wp;
			parse_str( $query, $query_vars );
			$query = [];
			foreach ( (array) $query_vars as $key => $value ) {
				if ( in_array( (string) $key, $wp->public_query_vars, true ) ) {
					$query[ $key ] = $value;
					if ( isset( $post_type_query_vars[ $key ] ) ) {
						$query['post_type'] = "any";
						$query['name']      = $value;
					}
				}
			}

			// Resolve conflicts between posts with numeric slugs and date archive queries.
			$query = wp_resolve_numeric_slug_conflicts( $query );

			//////////// ADDITION FOR CUSTOM POST TYPE SUPPORT BEGINS
			GLOBAL $wpdb;
			$post_types_query = 'SELECT DISTINCT post_type FROM ' . $wpdb->posts;
			$post_types = $wpdb->get_results( $post_types_query, ARRAY_N );
			$query['post_type'] = array_column($post_types,'0');
			//////////// ADDITION FOR CUSTOM POST TYPE SUPPORT ENDS

			// Do the query.
			$query = new WP_Query( $query );

			if ( ! empty( $query->posts ) && $query->is_singular ) {
				return $query->post->ID;
			} else {
				return 0;
			}
		}
	}
	return 0;
}
