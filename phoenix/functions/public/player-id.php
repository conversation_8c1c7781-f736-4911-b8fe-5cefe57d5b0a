<?php
// Used for decryption of PID (player id)
function decryptedPID($stringToDecrypt, $key = "Gpz01wQsgEXz23cQ") {

    // Bail early if string length is less than 32 characters
    if(strlen($stringToDecrypt) < 32) {
        return $stringToDecrypt;
    }

    /* AES decryption key is stored in ETCD,
        since we cant fetch ETCD via cloudways, we have to use it hardcoded
        Just for future reference, it is stored here
        /SYSTEM/PLAYER/ID_ENCRYPT_KEY
    */
    try {
        // Set the key and method
        $method = 'AES-128-ECB';

        // Convert hex to binary
        $encryptedData = hex2bin($stringToDecrypt);

        // Decrypt the data
        $decrypted = openssl_decrypt($encryptedData, $method, $key, OPENSSL_RAW_DATA);

        return $decrypted;
    } catch (Exception $e) {
        error_log("Can not decrypt stringToDecrypt={$stringToDecrypt}: " . $e->getMessage());
        return null;
    }
}

// Used for encryption of PID (player id)
function encryptedPID($stringToEncrypt, $key = "Gpz01wQsgEXz23cQ") {

    // Bail early if string length is 32 characters or more
    if(strlen($stringToEncrypt) >= 32) {
        return $stringToEncrypt;
    }

    try {
        // Set the key and method
        $method = 'AES-128-ECB';

        // Encrypt the data
        $encrypted = openssl_encrypt($stringToEncrypt, $method, $key, OPENSSL_RAW_DATA);

        // Convert to hex
        return bin2hex($encrypted);
    } catch (Exception $e) {
        error_log("Can not encrypt stringToEncrypt={$stringToEncrypt}: " . $e->getMessage());
        return null;
    }
}