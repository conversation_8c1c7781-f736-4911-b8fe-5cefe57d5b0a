<?php

/**
 * Check if given feature is available in the instance or not
 *
 * @param string $featureName full name of the feature
 * naming are:
 *      games/quiz
 *      games/letter-game
 *      campaigns/acq
 *      campaigns/acq/jackpot-games
 *      ...
 *      campaigns/crm
 *      campaigns/crm/left-and-right
 *      ...
 * */
function isFeatureActive($featureName)
{
    global $configArray;

    if (trim($featureName) == '') {
        return false;
    }
    $featureParts = explode('/', trim($featureName, '/'));

    $featureConfig = $configArray;
    foreach ($featureParts as $featurePart) {
        if (!isset($featureConfig[$featurePart])) {
            return false;
        }
        $featureConfig = $featureConfig[$featurePart];
    }

    return isFeatureActiveRecursively($featureConfig);
}

function isFeatureActiveRecursively($config)
{
    if (is_array($config)) {
        foreach ($config as $subConfig) {
            if (isFeatureActiveRecursively($subConfig)) {
                return true;
            }
        }
        return false;
    }

    return $config;
}
