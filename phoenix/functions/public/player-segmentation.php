<?php
function getLowestPlayerSegmentValue() {
    return PLAYER_SEGMENTS['inactive'];
}

function getContentForSegmentValue($segmentedOffers) {
    $playerSegmentValue = player()->getSegmentValue($segmentedOffers['player_segment_type']);

    if (in_array($playerSegmentValue, PLAYER_SEGMENTS)) {
        $playerSegmentHighEnough = false;

        // we iterate the PLAYER_SEGMENTS from high to low
        foreach (PLAYER_SEGMENTS as $segmentValue) {
            // check if player current segment is high enough to assign the offer content
            if ($playerSegmentHighEnough || $playerSegmentValue == $segmentValue) {
                $playerSegmentHighEnough = true;

                $offerType = $segmentValue . '_offer';
                // check if content exist for segment
                // we check for the title cause that is required field in acf
                if (
                    !empty($segmentedOffers[$offerType]['offer_content']['campaign_header']) ||  // interactive, wheel
                    !empty($segmentedOffers[$offerType]['content']['campaign_header']) || // left-and-right
                    !empty($segmentedOffers[$offerType]['image']['image_title']) || // offer-of-the-day
                    !empty($segmentedOffers[$offerType]['header']) // calendar-offers
                ) {
                    return $segmentedOffers[$offerType];
                }
            }
        }
    }

    // if no offer found for the player current segment return the lowest segment offer
    $offerType = getLowestPlayerSegmentValue() . '_offer';
    return $segmentedOffers[$offerType];

}
