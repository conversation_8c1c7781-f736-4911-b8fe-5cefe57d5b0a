<?php
/*
Query all options to be used for acf fields that are saved into wp_options table
*/
global $wpdb, $all_options;
$all_options = $wpdb->get_results("SELECT option_name, option_value FROM {$wpdb->options} WHERE option_name LIKE 'options_%'", 'OBJECT_K');

function get_field_tweaked($field_name = '', $field_type = '') {
    if(is_multisite()) {
        if ($field_type === 'option' || $field_type === 'options') {
            return get_field($field_name, $field_type);
        } else {
            return get_field($field_name);
        }
    }

    // Fallback to be used when there is no data in database,
    // if data found on database $field variable is mutated with data
    $field = null;

    if (! function_exists('acf_get_setting')) {
        return;
    }

    // Multi-lang support for acf fields
    $defaultLanguage = acf_get_setting('default_language');
    $currentLanguage = acf_get_setting('current_language');

    if(!empty($field_name)){
        if (!empty($field_type)) {
            if ($field_type === 'option' || $field_type === 'options') {
                global $all_options;

                if(isset($all_options['options_' . $field_name])) {
                    if(NULL !== $all_options['options_' . $field_name]->option_value) {
                        $field = maybe_unserialize($all_options['options_' . $field_name]->option_value);
                    }
                }

                if($currentLanguage !== $defaultLanguage) {
                    // If multi-language version of data is available, use it
                    if(isset($all_options['options_' . $currentLanguage . '_' . $field_name])) {
                        if(NULL !== $all_options['options_' . $currentLanguage . '_' . $field_name]->option_value) {
                            $field = maybe_unserialize($all_options['options_' . $currentLanguage . '_' . $field_name]->option_value);
                        }
                    }
                }

                // Optimize footer fields to use global $all_options variable, so that there will be no need to query for them again
                if (in_array($field_name, ['footer_left_area', 'footer_right_area'])) {


                    $langPrefix = '';

                    if($currentLanguage !== $defaultLanguage) {
                        $langPrefix = $currentLanguage . '_';
                    }

                    // When $field_name is 'footer_left_area' or 'footer_right_area'
                    // $field value contains how many links the field has, therefore we check if link count is 0 or higher
                    if ($field > 0) {
                        $links = [];
                        for ($i = 0; $i < $field; $i++) {
                            if (array_key_exists('options_' . $langPrefix . $field_name . '_' . $i . '_item_text', $all_options)) {
                                $links[$i]['item_text'] = $all_options['options_' . $langPrefix . $field_name . '_' . $i . '_item_text']->option_value;
                                $links[$i]['item_url'] = $all_options['options_' . $langPrefix . $field_name . '_' . $i . '_item_url']->option_value;
                            }
                        }
                        $field = $links;
                    }
                }
            }
        } else {
            $post_id = get_the_id();


            if ($currentLanguage && $currentLanguage !== $defaultLanguage) {
                $post_id .= '_' . $currentLanguage;
            }
            $field = get_post_meta($post_id, $field_name);
            $field = (is_array($field) && isset($field[0]) ? $field[0] : '');
        }
    }
    return $field;
}