<?php
/**
 * Search Tweak
 *
 * @param string   $search Search SQL for WHERE clause.
 * @param WP_Query $wp_query   The current WP_Query object.
 */
add_filter( 'posts_search', 'search_tweak', 10, 2 );
function search_tweak( $search, $wp_query ) {

    // Bail if we are not in the admin area
    if ( ! is_admin() ) {
        return $search;
    }

    // Bail if this is not the search query.
    if ( ! $wp_query->is_main_query() && ! $wp_query->is_search() ) {
        return $search;
    }

    // Get the value that is being searched.
    $search_string = get_query_var( 's' );

    // Bail if the search string is not an integer.
    if ( ! filter_var( $search_string, FILTER_VALIDATE_INT ) ) {

		if(!empty($search_string) && !is_admin()) {
			// Redirect to 404 if Blog is not enabled
			if (!isFeatureActive('blog')) {
				redirect_to_404();
			} else {
				$post_count = wp_count_posts();

				// Redirect to 404 if there's no 5 published blog posts at least
				if($post_count->publish < 5) {
					redirect_to_404();
				}
			}
		}

        return $search;
    }

	global $wpdb;
	/*
	* 	Ability to search by post ID on WP Admin.
    *	Return modified posts_search clause.
	*/
    return "AND $wpdb->posts.ID = '" . intval( $search_string )  . "'";
}

add_action( 'pre_get_posts', 'no_cpt_on_archives_and_search' );
function no_cpt_on_archives_and_search( $query ) {
	// Don't show custom post types on search results page
	if (!$query->is_admin && $query->is_search) {
		$query->set('post_type', array('post', /* 'page', */));
    }

	// Don't show custom post types on archive pages
	// TODO: Fix since it breaks /quiz/ archive page
	// if ( $query->is_archive() && $query->is_main_query() ) {
	// 	$query->set('post_type', array('post', /* 'page', */));
	// }
}
