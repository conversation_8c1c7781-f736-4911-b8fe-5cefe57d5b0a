<?php
$block = array_merge([
    'id'        => '',
    'className' => '',
    'anchor'    => '',
], $block);

global $content;
$content            = get_field('block_content');
$image              = get_field('block_image');
$layoutSettings     = get_field('layout_settings');
$layoutStyle        = get_field('layout_style');
$backgroundGroup    = get_field('block_background');
$termsAndConditions = get_field('terms_and_conditions_group');

$predictions = get_field('block_predictions');

$predictions = array_filter($predictions, function ($prediction) {
    if (getDateTime($prediction['matchup']['date']) >= getDateTime()) {
        return $prediction;
    }
});

$nowIsBiggerThanCutoff = false;
$displayExpertPrediction = false;

get_template_part('dynamic/parts/preview');

if (!empty($content['campaign_header']) && !empty($content['button_url']) && !empty($content['button_text'])) : ?>
    <?php
    $spMainClass = getClass([
        'sp-block sp-block-main background content-stretchable block-stretchable',
        'sp-block-main--' . $block['id'],
        'sp-block-main--vh-mobile-' . get_field('main_block_mobile_height'),
        'sp-block-main--vh-desktop-' . get_field('main_block_desktop_height'),
        $block['className'],
    ]);

    addBackgroundInlineStyle('.sp-block-main--' . $block['id'], $backgroundGroup);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spMainClass; ?>">

        <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

        <?php
        $contentContainerClass = getClass([
            'content-margin campaign__content container wrapper sp-block-main__content',
            [
                'condition' => ($layoutStyle === 'centered'),
                'name'      => 'campaign__content--centered',
                'else-name' => 'wrapper--md-80',
            ],
            [
                'condition' => ($layoutStyle === 'centered' &&
                    empty($layoutSettings['full_width'])),
                'name' => 'wrapper--tight'
            ],
            [
                'condition' => !empty($content['invert_text_color']),
                'name'      => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ],
            [
                'condition' => !empty($layoutSettings['full_width']),
                'name'      => 'wrapper--wider'
            ]
        ]);
        ?>
        <div class="<?= $contentContainerClass; ?>">
            <form class="js-form-predictions js-cookie-formdata js-form-quickbet">
            <?php
            // Backwards compatibility with 'grid-left-and-right' class fallback
            $layoutStyleClass = 'grid-' . ($layoutStyle ?? 'left-and-right');

            $gridLeftAndRightContainerClass = getClass([
                $layoutStyleClass,
                $layoutStyleClass . '--has-image-above-heading',
                [
                    'condition' => !empty($layoutSettings['columns_size']),
                    'name'      => $layoutStyleClass . '--' . $layoutSettings['columns_size'],
                ],
            ]);
            ?>
            <div class="<?= $gridLeftAndRightContainerClass; ?>">
                <!-- content -->
                <?php
                $gridLeftColumnClass = getClass([
                    $layoutStyleClass . '__left',
                    'sp-block__animation',
                    'sp-block__animation--fade-in-left',
                    [
                        'condition' => !empty($content['vertical_alignment']),
                        'name'      => $layoutStyleClass . '__left--' . $content['vertical_alignment'],
                    ]
                ]);
                ?>
                <div class="<?= $gridLeftColumnClass; ?>">
                    <?php if (!empty($content['tags'])) : ?>
                        <div class="tags">
                            <?php foreach ($content['tags'] as $tag) : ?>
                                <span class="tag"><?= $tag['tag'] ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    <?php get_template_part('campaigns/parts/image-above-title'); ?>
                    <?php get_template_part('campaigns/parts/title'); ?>
                    <?php get_template_part('campaigns/parts/subtitle'); ?>
                    <?php get_template_part('campaigns/parts/description'); ?>

                    <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'above_cta'])); ?>
                </div>

                <div class="<?= $layoutStyleClass; ?>__right sp-block__animation sp-block__animation--fade-in-right">
                    <?php if (!empty($predictions)) : ?>
                    <?php
                    // this is for displaying the matches in different sizes and columns according to the count of predictions
                    // make sure if you change this numbers to change the css classes too!!!!
                    $predictionComponentSizes = [
                        'large' => 8,
                        'medium' => 12,
                        'small' => 20,
                    ];
                    $countPredictions = count($predictions);
                    $predictionsClass = '';
                    if ($countPredictions > $predictionComponentSizes['small']) {
                        $predictionsClass = ' prediction__grid--columns prediction__grid--small prediction__grid--columns-' . ceil($countPredictions / 2);

                    } elseif ($countPredictions > $predictionComponentSizes['medium']) {
                        $predictionsClass = ' prediction__grid--columns prediction__grid--medium prediction__grid--columns-' . ceil($countPredictions / 2);

                    } elseif ($countPredictions > $predictionComponentSizes['large']) {
                        $predictionsClass = ' prediction__grid--medium';
                    }
                    ?>
                    <div class="prediction__grid<?= $predictionsClass; ?>">
                        <?php
                        foreach ($predictions as $i => $prediction) {
                            $prediction['class'] = null;
                            $prediction['user_answer'] = null;
                            $prediction['correct_answer'] = null;

                            include(locate_template('campaigns/parts/beat-the-expert/matchup.php', false, false));
                        }
                        ?>
                    </div>

                    <?php ob_start(); ?>
                    <?php
                    $buttonClass = getClass([
                        'btn',
                        [
                            'condition' => !empty($content['button_color']),
                            'name'      => 'btn--' . $content['button_color'],
                        ],
                        [
                            'condition' => !empty($content['button_size']),
                            'name'      => 'btn--' . $content['button_size'],
                        ],
                        [
                            'condition' => !empty($content['button_animation']),
                            'name'      => 'btn--animation-' . $content['button_animation'],
                        ],
                        [
                            'condition' => !empty($content['button_tracking']),
                            'name'      => 'js-' . $content['button_tracking'],
                            'else-name' => 'js-button-claim',
                        ]
                    ]);
                    $quickBetButtonClass = $buttonClass . ' btn--outlined js-quickbet';
                    ?>
                    <a class="<?= $quickBetButtonClass; ?>" href="#"><?= get_field('block_quick_bet_button_lable'); ?></a>
                    <a href="<?= $content['button_url']; ?>" class="<?= $buttonClass; ?>">
                        <?= $content['button_text']; ?>
                    </a>
                    <?php
					$stickyButtonElement = ob_get_clean();
					get_template_part('campaigns/parts/cta-button-sticky', null, [
						'button' => $stickyButtonElement,
						'sticky' => empty($content['not_sticky_mobile_button']),
						'split' => true,
					]);
					?>
                    <?php else : ?>
                        <div class="prediction__grid">
                            <div class="prediction__field prediction__not-available">
                                <div class="prediction__not-available__title"><?= get_field('block_no_matches_available'); ?></div>
                                <div  class="prediction__not-available__description"><?= get_field('block_check_out_other_offers'); ?></div>
                            </div>
                        </div>
                        <div class="btn__wrapper">
                            <a class="btn" href="<?= get_field('block_check_out_other_offers_button_url'); ?>" class="<?= get_field('block_check_out_other_offers_button_lable'); ?>">
                                <?= get_field('block_check_out_other_offers_button_lable'); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                    <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>
                </div>
            </div>
            </form>

            <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'below_content'])); ?>
        </div>
    </section>
<?php
endif;
