<?php
global $blockContent, $blockButton;
if(!is_array($args)) $args = []; // Fallback for array_merge below

$args = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
    'content' => '',
], $args);
$type = get_field('block_type');
$fontColor = 'normal-font-color';
if (get_field('invert_text_color')) {
    $fontColor = 'invert-font-color';
}
$background = get_field('block_background');
addBackgroundInlineStyle('.sp-block--' . $args['id'], $background);

get_template_part('dynamic/parts/preview');

$spBlockClass = getClass([
    'sp-block',
    'sp-block--' . $type,
    'sp-block--' . $fontColor,
    'sp-block--' . $args['id'],
    $args['className'],
    'block-stretchable',
    (!empty($args['data']['minimize_top_padding']) ? ' sp-block--minimize-padding-top' : ''),
    (!empty($args['data']['minimize_bottom_padding']) ? ' sp-block--minimize-padding-bottom' : ''),
]);

if (at_least_one_has_value($args['content'], $blockContent, array_keys_have_values($blockButton, ['button_text', 'button_url']))) : ?>
<section id="<?= $args['anchor']; ?>" class="<?= $spBlockClass; ?>">
    <?php get_template_part('campaigns/parts/background-video', null, $background); ?>
    <?php get_template_part('campaigns/parts/background-lottie', null, $background); ?>
    <div class="container sp-block__container">
        <div class="wrapper sp-block__animation sp-block__animation--fade-in-top">
            <?php get_template_part('dynamic/parts/block', 'content'); ?>
        </div>

        <div class="sp-block__animation sp-block__animation--fade-in-center">
            <?= $args['content']; ?>
        </div>

        <?php if (array_keys_have_values($blockButton, ['button_text', 'button_url'])) : ?>
            <div class="wrapper sp-block__animation sp-block__animation--fade-in-bottom">
                <?php get_template_part('dynamic/parts/block', 'cta'); ?>
            </div>
        <?php endif; ?>
    </div>
</section>
<?php
endif;
