/* Lint ------------------- */
/* global documentReady, get<PERSON><PERSON>ie, dataL<PERSON>er<PERSON><PERSON><PERSON>, fetchTrackingWP, main_params */

documentReady(function () {
    let templateName,
        isGoSite  = main_params.site == ('go'),
        isRgSite  = main_params.site == ('rg'),
        isFaqSite = main_params.site == ('faq');

    let bodyClasses  = document.body.classList;
    let isPage       = bodyClasses.contains('article') || bodyClasses.contains('page');
    let isDynamic    = bodyClasses.contains('dynamic');
    let isStartPage  = bodyClasses.contains('start-page');
    let isPNP        = bodyClasses.contains('pnp');
        templateName = bodyClasses[1] || bodyClasses[0];

    // handle templates: dynamic, dynamic-start-page, normal-start-page
    templateName = (isDynamic ? "dynamic" :  templateName);
    templateName = (isStartPage ? (isPNP ? bodyClasses[2] : bodyClasses[1]) + "-start-page" : templateName);

    // handle pages: e.g. page-go, page-rg, page-faq
    templateName = (isPage ? "page-go" : templateName); // hardcode fallback for Go pages
    templateName = (isPage && (isGoSite || isRgSite || isFaqSite) ? "page-" + main_params.site : templateName);

    let brand_root = window.location.hostname.split('.').slice(-2).join('.');

    // Define missing cookies
    if(!getCookie('locale')) {
        document.cookie = "locale=" + main_params.locale + ";domain=" + brand_root + ";path=/";
    }

    // Fetch WP tracking API
    if(getCookie('locale')) {
        fetchTrackingWP();
    }

    var trackEventTriggerElements = [
        document.querySelectorAll('.js-button-claim'),
        document.querySelectorAll('.js-button-login'),
        document.querySelectorAll('.js-button-register'),
        document.querySelectorAll('.footer a'),
        document.querySelectorAll('.navigation__container--promo .menu-item'), // Header Menu links on desktop
        document.querySelectorAll('.navigation__menu--mobile-tabs a'), // Global Nav Menu links on mobile
    ];

    trackEventTriggerElements.forEach((elements, index) => {
        if (elements.length > 0) {
            elements.forEach((element) => {
                element.addEventListener("click", function (clickEvent) {
                    let eventCategory = '';
                    let eventAction   = 'Click'
                    let eventLabel    = ''
                    let eventValue    = ''; //(element.href.length && element.href);

                    let body   = document.querySelector('body');
                    let parent = element.parentElement;

                    // Define category and action according to CTA type


                    // Desktop header menu and Mobile menu, (which is called "global nav" during implementation, it's a tabbed menu)
                    if (element.classList.contains('menu-item') !== null) {
                        eventCategory = 'Top Nav Item';
                        eventAction   = 'Click';

                        // Hardcode datalayer values for DESKTOP MENU items based on href or title
                        let href = (element.getAttribute('href') || element.href || '').toLowerCase();
                        let title = (element.getAttribute('title') || '').toLowerCase();
                        let text = (clickEvent.target.textContent || clickEvent.target.innerText || '').toLowerCase();

                        // Check for specific keywords in href or title and assign hardcoded labels
                        if (href.includes('sportsbook') || title.includes('sportsbook') || text.includes('Sport')) {
                            eventLabel = 'sportsbook';
                        } else if (href.includes('live-betting') || href.includes('livebetting') || title.includes('live-betting') || title.includes('live betting')) {
                            eventLabel = 'live-betting';
                        } else if (href.includes('casino') && !href.includes('live') || title.includes('casino') && !title.includes('live')) {
                            eventLabel = 'casino';
                        } else if (href.includes('live-casino') || href.includes('livecasino') || title.includes('live-casino') || title.includes('live casino')) {
                            eventLabel = 'live-casino';
                        } else if (href.includes('we-spin') || href.includes('wespin') || title.includes('we-spin') || title.includes('wespin')) {
                            eventLabel = 'we-spin';
                        } else if (href.includes('virtuals') || title.includes('virtuals') || text.includes('Virtuals')) {
                            eventLabel = 'virtuals';
                        } else if (href.includes('tvbet') || title.includes('tvbet')) {
                            eventLabel = 'tvbet';
                        } else {
                            // Fallback to data-gtm-key attribute or text content
                            eventLabel = clickEvent.target.getAttribute('data-gtm-key') ||
                                         element.getAttribute('data-gtm-key') ||
                                         title ||
                                         text;
                        }

                        // Debug logging for menu item tracking
                        if (typeof main_params !== 'undefined' && main_params.debug) {
                            console.log('Menu Item Tracking Logs:', {
                                href_attribute: href,
                                title_attribute: title,
                                text_of_link: text,
                                eventLabel: eventLabel,
                                element: element
                            });
                        }
                    }

                    // Product icons on Start Page
                    if (clickEvent.target.closest('.sp-block-products__item') !== null) {
                        eventCategory = 'product buttons';
                        eventAction   = 'Click';
                        eventLabel    = clickEvent.target.text;
                    }

                    if (element.classList.contains('js-button-claim')) {
                        eventCategory = 'Offer';
                        eventAction   = 'Claim';
                    }

                    if (element.classList.contains('js-button-register')) {
                        eventCategory = 'Registration';
                        eventAction = 'Click';
                    }

                    if (element.classList.contains('js-button-login')) {
                        eventCategory = 'Login';
                        eventAction   = 'Log-In';
                    }

                    if (element.classList.contains('footer a')) {
                        eventCategory = 'Footer';
                        eventAction   = 'promotions';
                    }


                    // If header CTAs
                    if(parent.classList.contains('login-wrapper') || parent.classList.contains('navigation__login-mobile')) {
                        eventLabel = 'Header';
                    }

                    // Define final variables
                    eventAction = eventAction.length ? eventAction : 'Click';
                    eventLabel = eventLabel.length ? eventLabel : `Wordpress - ${eventLabel} - ${templateName}`;

                    // If start page template
                    // if(body.classList.contains('start-page')) {
                    //     eventAction = 'Start';
                    // }

                    // Handle the dataLayer info
                    dataLayerHandler({
                        'event'        : 'trackEvent',
                        'eventCategory': eventCategory,
                        'eventAction'  : eventAction,
                        'eventLabel'   : eventLabel,
                        'eventValue'   : eventValue
                    });
                });
            });
        }
    });
});
