<?php
getBlockCommonFields();
$benefits = get_field('block_benefits');

if (!empty($benefits) && is_array($benefits)) {
    $benefits = array_map(function ($benefit) {
        if (is_array($benefit) && !all_have_values(...$benefit)) {
            return null;
        }
        return $benefit;
    }, $benefits);

    $benefits = array_filter($benefits);
}

if (!empty($benefits)) {
    ob_start();
?>
    <div class="wrapper">
        <div class="sp-block-benefits">
            <?php foreach ($benefits as $benefit) : ?>
                <div class="sp-block-benefits__item">
                    <img src="<?= $benefit['image']; ?>" alt="<?= wp_strip_all_tags($benefit['title']); ?>" class="sp-block-benefits__image" height="100" width="100" />
                    <h3 class="sp-block-benefits__title"><?= $benefit['title']; ?></h3>
                    <p class="sp-block-benefits__text"><?= $benefit['text']; ?></p>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('dynamic/parts/block', null, $block);
