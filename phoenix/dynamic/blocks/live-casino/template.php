<?php
getBlockCommonFields();
$liveCasinos = get_field('block_live_casinos');

if (!empty($liveCasinos) && is_array($liveCasinos)) {
    $liveCasinos = array_map(function ($liveCasino) {
        if (is_array($liveCasino) && !all_have_values(...$liveCasino)) {
            return null;
        }
        return $liveCasino;
    }, $liveCasinos);

    $liveCasinos = array_filter($liveCasinos);
}

if (!empty($liveCasinos)) {
    ob_start();
?>
    <div class="wrapper">
        <div class="sp-block-live-casino">
            <?php foreach ($liveCasinos as $casino) : ?>
                <a class="sp-block-live-casino__item sp-block__card" href="<?= $casino['url']; ?>" title="<?= $casino['title']; ?>">
                    <img src="<?= $casino['image']; ?>" alt="<?= wp_strip_all_tags($casino['title']); ?>" class="sp-block-live-casino__image" height="230" width="230" />
                    <h3 class="sp-block-live-casino__title"><?= $casino['title']; ?></h3>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
<?php
    $block['content'] = ob_get_clean();
}

get_template_part('dynamic/parts/block', null, $block);
