<?php
add_action('wp_enqueue_scripts', 'add_main_script');
add_action('admin_enqueue_scripts', 'add_main_script');
function add_main_script()
{
    if ( ! is_admin() ) {
        wp_deregister_script('jquery');
        wp_register_script('jquery', 'https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js', [], null, true);
        // Register 'main' script for frontend
        wp_register_script('main', get_template_directory_uri() . '/dist/main.js', ['jquery'], filemtime(get_template_directory() . '/dist/main.js'), true);

        wp_register_style('background-inline-style', false, [], RELEASE_VERSION);
    } else {
        // Register 'main' script for main_params to be loaded on admin too
        wp_register_script('main', null);
    }

    global $current_user;
    // Check get parameter first, then transient, then fallback
    $simulated_time = $_GET['time_value'] ?? (get_transient('px_time_value_' . $current_user->ID) ?? getNow()->format('Y-m-d\TH:i'));

    wp_localize_script('main', 'main_params', [
        'ajax_url'              => admin_url('admin-ajax.php'),
        'brand_url'             => brandUrl(),
        'login_url'             => loginUrl(),
        'loggedin'              => player()->isLoggedIn(),
        'locale'                => get_locale(),
        'brand'                 => CURRENT_BRAND,
        'currency'              => CURRENT_CURRENCY,
        'currency_symbol'       => CURRENT_CURRENCY_SYMBOL,
        'timezone'              => CURRENT_TIMEZONE,
        'region'                => CURRENT_REGION,
        'authority'             => CURRENT_AUTHORITY,
        'language'              => CURRENT_LANGUAGE,
        'country'               => CURRENT_COUNTRY,
        'tracking'              => TRACKING_PARAMS,
        'debug'                 => DEBUG_MODE,
        'dev'                   => DEV_ENV,
        'local'                 => LOCAL_ENV,
        'proxy'                 => PROXY_ENV,
        'site'                  => SITE_TYPE,
        'sportsbook'            => defined('SPORTSBOOK_PROVIDER') ? SPORTSBOOK_PROVIDER : '',
        'simulated_time'        => $simulated_time,
        'simulated_user'        => SIMULATED_USER,
    ]);

    wp_localize_script('main', 'translation',
        [
            'readmore' => [
                'more' => get_field_tweaked('readmore-more', 'option'),
                'less' => get_field_tweaked('readmore-less', 'option'),
            ],
            'countdown' => [
                'countdown_d'              => get_field_tweaked('countdown--d', 'option'),
                'countdown_h'              => get_field_tweaked('countdown--h', 'option'),
                'countdown_m'              => get_field_tweaked('countdown--m', 'option'),
                'countdown_s'              => get_field_tweaked('countdown--s', 'option'),
                'translation_and'          => get_field_tweaked('translation_and', 'option'),
                'pre_countdown'            => get_field_tweaked('pre_countdown', 'option'),
                'translation_new_offer_in' => get_field_tweaked('translation_new_offer_in', 'option'),
                'post_countdown'           => get_field_tweaked('post_countdown', 'option'),
                'offer_expired'            => get_field_tweaked('offer_expired', 'option'),
                'offer_ends_in'            => get_field_tweaked('offer_ends_in', 'option'),
                'campaign_resumes_in'      => get_field_tweaked('campaign_resumes_in', 'option'),
                'prediction_closes_in'     => get_field_tweaked('prediction_closes_in', 'option'),
                'predictions_are_closed'   => get_field_tweaked('predictions_are_closed', 'option'),
            ]
        ]
    );
    if ( ! is_admin() ) {
        wp_enqueue_script('jquery');
    }
    wp_enqueue_script('main');

    if(!is_page() && !is_blog()) {
        // Remove Gutenberg Block Library CSS from loading on the frontend
        wp_dequeue_style( 'wp-block-library' );
        wp_dequeue_style( 'wp-block-library-theme' );
        wp_dequeue_style( 'wc-blocks-style' ); // Remove WooCommerce block CSS
    }
}

add_action('get_footer', 'add_background_style');
function add_background_style()
{
    wp_enqueue_style('background-inline-style');
    global $backgroundStyle;
    if(!empty($backgroundStyle)) {
        return wp_add_inline_style('background-inline-style', $backgroundStyle);
    }
}
