/* Lint ------------------- */
/* global main_params, dataLayerHandler, pnp_tracking */

/*

Tracking API below returns information about logged-in user,
if visitor is not logged in then it returns general info like region and currency based on visitor's IP address

*/
async function fetchTrackingWP() {
    let templateName,
        isGoSite = main_params.site == ('go'),
        isRgSite = main_params.site == ('rg'),
        isFaqSite = main_params.site == ('faq');

    let bodyClasses = document.body.classList;
    let isPage = bodyClasses.contains('article') || bodyClasses.contains('page');
    let isDynamic = bodyClasses.contains('dynamic');
    let isStartPage = bodyClasses.contains('start-page');
    let isPNP = bodyClasses.contains('pnp');
    templateName = bodyClasses[1];
    let eventInfo;

    // handle templates: dynamic, dynamic-start-page, normal-start-page
    templateName = (isDynamic ? "dynamic" :  templateName);
    templateName = (isStartPage ? (isPNP ? bodyClasses[2] : bodyClasses[1]) + "-start-page" : templateName);

    // handle pages: e.g. page-go, page-rg, page-faq
    templateName = (isPage ? "page-go" : templateName); // hardcode fallback for Go pages
    templateName = (isPage && (isGoSite || isRgSite || isFaqSite) ? "page-" + main_params.site : templateName);

    try {
        // Use WordPress AJAX endpoint instead of direct tracking endpoint
        if (typeof pnp_tracking !== 'undefined' && pnp_tracking.ajax_url) {
            const formData = new FormData();
            formData.append('action', 'pnp_tracking');
            formData.append('nonce', pnp_tracking.nonce);
            formData.append('tracking_action', 'get_base_data');

            const response = await fetch(pnp_tracking.ajax_url, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success && result.data) {
                eventInfo = result.data;
                eventInfo['event'] = 'initial-meta';
                eventInfo['frame'] = 'wp';
                eventInfo['pixel_language'] = main_params.language;
                eventInfo['wp_template'] = templateName;
            } else {
                throw new Error('Failed to get tracking data from WordPress endpoint');
            }
        } else {
            throw new Error('WordPress AJAX endpoint not available');
        }
    } catch (error) {
        // Fallback when WordPress AJAX endpoint fails
        if (main_params.debug == "1") {
            console.log("Tracking not initialized:", error.message);
        }

        // Create default eventInfo
        eventInfo = {
            'event': 'initial-meta',
            'frame': 'wp',
            'pixel_language': main_params.language,
            'wp_template': templateName
        };
    }

    dataLayerHandler(eventInfo);
}