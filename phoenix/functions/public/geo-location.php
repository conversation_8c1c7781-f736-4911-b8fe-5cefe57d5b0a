<?php

// Validate IP address
function isValidIP($ip) {
    if (
        !filter_var(
            $ip,
            FILTER_VALIDATE_IP,
            FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        )
        && !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6 | FILTER_FLAG_NO_PRIV_RANGE)
    ) {
        return false;
    }

    return true;
}

// Get current user IP from headers
function getIP() {
    $remoteKeys = [
        'X-Worker-Real-IP',
        'CF-Connecting-IP',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_CLIENT_IP',
        'HTTP_X_FORWARDED',
        'HTTP_FORWARDED_FOR',
        'HTTP_FORWARDED',
        'REMOTE_ADDR',
        'HTTP_X_CLUSTER_CLIENT_IP',
    ];

    foreach ($remoteKeys as $key) {
        if ($address = getenv($key)) {
            foreach (explode(',', $address) as $ip) {
                if (isValidIP($ip)) {
                    do_action('qm/info', 'getIP -> ' . $ip);

                    return $ip;
                }
            }
        }
    }

    return '*********';
}

// Get current user country code XX from headers
// If no headers are found, then perform a local check
function getCountryCode() {
    if (isset($_SERVER)) {
        // Key below refers to visitor's country data
        if (array_key_exists('HTTP_CF_IPCOUNTRY', $_SERVER)) {
            $response = $_SERVER['HTTP_CF_IPCOUNTRY'];
        } elseif (array_key_exists('HTTP_X_COUNTRYCODE', $_SERVER)) {
            $response = $_SERVER['HTTP_X_COUNTRYCODE'];
        } elseif (getenv('HTTP_X_FORWARDED_COUNTRY')) {
            /* Since PHP supports white space in array keys,
            we can flip the array and use isset() to check the existence of value,
            it is much faster than array_search() or in_array() */
            $countryNamesAsKey = array_flip(COUNTRIES);
            $response          = $countryNamesAsKey[getenv('HTTP_X_FORWARDED_COUNTRY')] ?? false;
        }

        if(empty($response)) {
            $api = new Api('http://geoip:8080/json/' . getIP());
            $response = $api->get();

            // Fallback if http://geoip:8000 alias fails
            if(empty($response)) {
                $api = new Api('https://www.comeon.com/geoip');
                $response = $api->get();
            }

            if (!empty($response['country_code'])) {
                $response = $response['country_code'];
            } elseif (!empty($response['result'])) {
                if (is_string($response['result'])) {
                    $response['result'] = json_decode($response['result'], true);
                }
                if (!empty($response['result']['countryCode'])) {
                    $response = $response['result']['countryCode'];
                }
            }
        }

        if($response === 'CA') {
            $api = new Api('http://ip-api.com/json/' . getIP());
            $response = $api->get();

            if($response['region'] == 'ON') {
                do_action('qm/debug', 'Ontario exception triggered');
                $response = 'ON';
            }
        }
    }

    do_action('qm/info', 'getCountryCode() -> ' . $response);

    return $response;
}