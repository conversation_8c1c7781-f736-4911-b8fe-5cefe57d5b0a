<?php
getBlockCommonFields();
$media = get_field('media');
$sidecol_position = get_field('side_column_position');
$sidecol = get_field('side_column');

if (!empty($sidecol) && is_array($sidecol)) {
    $sidecol = array_map(function ($col) {
        if (empty($col['title']) || empty($col['text'])) {
            return null;
        }
        return $col;
    }, $sidecol);

    $sidecol = array_filter($sidecol);
}

ob_start();
?>
<div class="wrapper">
    <div class="sp-block-media sp-block-media--<?= $sidecol_position ?>">
        <div class="sp-block-media__column-left">
            <?php get_template_part('campaigns/parts/offer-media', null, $media); ?>
        </div>
        <?php if (!empty($sidecol)) : ?>
        <div class="sp-block-media__column-right sidecol">
            <?php foreach ($sidecol as $column) : ?>
                <?php $columnClasses = 'sidecol__column sp-block__card' . ( empty($column['icon']) ? ' sidecol__column--block': '' ); ?>
                <div class="<?= $columnClasses; ?>">
                    <?php if (!empty($column['icon'])) : ?>
                        <img src="<?= $column['icon']; ?>" alt="<?= wp_strip_all_tags($column['title']); ?>" height="100" width="100" />
                    <?php endif; ?>
                    <div class="sidecol__column-text">
                        <h4><?= $column['title']; ?></h4>
                        <p><?= $column['text']; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php
$block['content'] = ob_get_clean();

get_template_part('dynamic/parts/block', null, $block);
