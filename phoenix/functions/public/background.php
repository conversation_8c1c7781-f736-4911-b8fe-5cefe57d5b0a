<?php
/**
 * Add inline style for acf background component
 *
 * @param string $styleSelector = '.campaign .background'
 * @param array $backgroundGroup = get_field(acf component)
 * @return string
 */
function addBackgroundInlineStyle($styleSelector, $backgroundGroup)
{
    $backgroundType  = (!empty($backgroundGroup['background_type']) ? $backgroundGroup['background_type'] : 'background_image');
    $background = '';
    $mobileBackground = '';

    // If background color is set
    if (!empty($backgroundGroup['background_color'])) {
        $background .= 'background-image:none;background-color:' . $backgroundGroup['background_color'] . ';';
    }

    switch ($backgroundType) {
        case 'background_image':
            if (!empty($backgroundGroup['background_image'])) {
                $background .= "background-image:url(" . $backgroundGroup['background_image'] . ");";
            }

            if (!empty($backgroundGroup['mobile_background_image'])) {
                $mobileBackground = "background-image:url(" . $backgroundGroup['mobile_background_image'] . ");";
            }

            // If background options are set
            if (!empty($backgroundGroup['background_options'])) {
                $background .= 'background-size:' . $backgroundGroup['background_size'] . ';';
                $background .= 'background-position:' . $backgroundGroup['background_alignment'] . ';';
                $mobileBackground .= 'background-size:' . $backgroundGroup['mobile_background_size'] . ';';
                $mobileBackground .= 'background-position:' . $backgroundGroup['mobile_background_alignment'] . ';';
            }
        break;

        case 'background_pattern':
            if (!empty($backgroundGroup['background_image'])) {
                $background .= 'background-image:url(' . $backgroundGroup['background_image'] . ');background-size:auto;background-repeat:repeat;';
            }
        break;

        case 'background_gradient':
            $background .= 'background:' . $backgroundGroup['first_background_gradient_color'] . ';';
            $background .= 'background:linear-gradient(' . $backgroundGroup['background_gradient_angle'] .'deg, ' . $backgroundGroup['first_background_gradient_color'] . ' 0%, ' . $backgroundGroup['second_background_gradient_color'] . ' 100%);';
        break;

        case 'background_video':
            if (!empty($backgroundGroup['background_options'])) {
                $background .= 'object-fit:' . ($backgroundGroup['background_size'] === "auto" ? 'fill': $backgroundGroup['background_size']) . ';';
                $mobileBackground .= 'object-fit:' . ($backgroundGroup['mobile_background_size'] === "auto" ? 'fill': $backgroundGroup['mobile_background_size']) . ';';
            }
        break;
    }

    global $backgroundStyle;
    if (!empty($background)) {
        $backgroundStyle .= $styleSelector .'{'.$background.'}';

        if (!empty($mobileBackground)) {
            $backgroundStyle .= '@media screen and (max-width:480px){'. $styleSelector .'{'.$mobileBackground.'}}';
        }


        // Handle background size rules for Video Background
        if($backgroundType === 'background_video') {
            $styleSelector = $styleSelector . ' video';

            $backgroundStyle .= $styleSelector .'{'.$background.'}';

            if (!empty($mobileBackground)) {
                $backgroundStyle .= '@media screen and (max-width:768px){'. $styleSelector .'{'.$mobileBackground.'}}';
            }
        }
    }

}