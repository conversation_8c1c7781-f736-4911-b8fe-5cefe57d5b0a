<?php
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);

global $content;
$content            = get_field('block_content');
$image              = get_field('block_image');
$layoutSettings     = get_field('layout_settings');
$layoutStyle        = get_field('layout_style');
$backgroundGroup    = get_field('block_background');
$termsAndConditions = get_field('terms_and_conditions_group');

$enableCountdown = get_field('enable_countdown');

if ($enableCountdown) {
    $today  = getDateTime('today');
    $now    = getDateTime();

    $start               = getDateTime(get_field('countdown_start'));
    $end                 = getDateTime(get_field('countdown_to'));
    $countdownType       = get_field('countdown_type') ?? 'regular';
    $countdownClockColor = get_field('countdown_clock_color');
    $countdown24h        = get_field('countdown_24');

    // Countdown related variables
    $countdownStart = $today;
    $countdownEnd = $start;
    $countdownStatus = COUNTDOWN_STARTS_IN;   // Not yet started

    if ($now > $start) {
        $countdownStart = $start;
        $countdownEnd = $end;
        $countdownStatus = COUNTDOWN_EXPIRED;

        if ($now < $countdownEnd) {
            // overwrite initial content once time conditions are met
            $countdownStatus = COUNTDOWN_CAMPAIGN_ENDS_IN;

            if ($countdown24h) {
                $countdownStart = $today;
                $countdownEnd = getDateTime('tomorrow');
            }
        }
    }
}

$dataFieldLable = get_field('optin_data_lable');

if (!empty($_POST['email']) || !empty($_POST['terms'])) {
    $sentUserEmail = sanitize_email($_POST['email']);
    $sentDataField = sanitize_text_field($_POST['data']);
    $sentUserTerms = sanitize_text_field($_POST['terms']);

    $status = [
        'type' => 'error',
        'message' => get_field('optin_notification_error')
    ];

    if (filter_var($sentUserEmail, FILTER_VALIDATE_EMAIL) &&
        $sentUserTerms == "true" &&
        isRecaptchaValid($_POST['recaptcha_response'])
    ) {
        global $wpdb;
        // Initial user predictions submission
        $package = [
            'post_id' => get_the_ID(),
            'report_id' => get_field('option_reports'),
            'email' =>  $sentUserEmail,
            'data' =>  !empty($dataFieldLable) ? serialize([$dataFieldLable => $sentDataField]) : '',
            'time' => getNow()->format('Y-m-d H:i:s')
        ];

        if ($wpdb->insert(BLOCK_OPTIN_TABLE_NAME, $package)) {
            $status = [
                'type' => 'success',
                'message' => get_field('optin_notification_success')
            ];
        }
    }

    get_template_part('components/notification', null,  array_merge($status, [
        'show' => true,
        'hide' => true,
    ]));

}

get_template_part('dynamic/parts/preview');

if (!empty($content['campaign_header']) && !empty($content['button_url']) && !empty($content['button_text'])) : ?>
    <?php
    $spMainClass = getClass([
        'sp-block sp-block-main background content-stretchable block-stretchable',
        'sp-block-main--' . $block['id'],
        'sp-block-main--vh-mobile-' . get_field('main_block_mobile_height'),
        'sp-block-main--vh-desktop-' . get_field('main_block_desktop_height'),
        $block['className'],
    ]);

    addBackgroundInlineStyle('.sp-block-main--' . $block['id'], $backgroundGroup);
    ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spMainClass; ?>">

        <?php get_template_part('campaigns/parts/background-video', null, $backgroundGroup); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $backgroundGroup); ?>

        <?php
        $contentContainerClass = getClass([
            'content-margin campaign__content container wrapper sp-block-main__content',
            [
                'condition' => ($layoutStyle === 'centered'),
                'name'      => 'campaign__content--centered',
                'else-name' => 'wrapper--md-80',
            ],
            [
                'condition' => ($layoutStyle === 'centered' &&
                    empty($layoutSettings['full_width'])),
                'name' => 'wrapper--tight'
            ],
            [
                'condition' => !empty($content['invert_text_color']),
                'name'      => 'campaign__content--invert-font-color',
                'else-name' => 'campaign__content--normal-font-color',
            ],
            [
                'condition' => !empty($layoutSettings['full_width']),
                'name'      => 'wrapper--wider'
            ]
        ]);
        ?>
        <div class="<?= $contentContainerClass; ?>">
            <form id="<?= $block['id']; ?>" class="js-form-predictions" method="POST" action="<?= get_permalink(); ?>">
            <?php
            // Backwards compatibility with 'grid-left-and-right' class fallback
            $layoutStyleClass = 'grid-' . ($layoutStyle ?? 'left-and-right');

            $gridLeftAndRightContainerClass = getClass([
                $layoutStyleClass,
                $layoutStyleClass . '--has-image-above-heading',
                [
                    'condition' => !empty($layoutSettings['columns_size']),
                    'name'      => $layoutStyleClass . '--' . $layoutSettings['columns_size'],
                ],
            ]);
            ?>
            <div class="<?= $gridLeftAndRightContainerClass; ?>">
                <!-- content -->
                <?php
                $gridLeftColumnClass = getClass([
                    $layoutStyleClass . '__left',
                    'sp-block__animation',
                    'sp-block__animation--fade-in-left',
                    [
                        'condition' => !empty($content['vertical_alignment']),
                        'name'      => $layoutStyleClass . '__left--' . $content['vertical_alignment'],
                    ]
                ]);
                ?>
                <div class="<?= $gridLeftColumnClass; ?>">
                    <?php if (!empty($content['tags'])) : ?>
                        <div class="tags">
                            <?php foreach ($content['tags'] as $tag) : ?>
                                <span class="tag"><?= $tag['tag'] ?></span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    <?php get_template_part('campaigns/parts/image-above-title'); ?>
                    <?php get_template_part('campaigns/parts/title'); ?>
                    <?php get_template_part('campaigns/parts/subtitle'); ?>
                    <?php get_template_part('campaigns/parts/description'); ?>

                    <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'above_cta'])); ?>

                    <?php if ($enableCountdown) {
                        get_template_part('campaigns/parts/countdown-sticky', null, [
                            'status' => $countdownStatus,
                            'time_end' => $countdownEnd,
                            'time_start' => $countdownStart,
                            '24h' => $countdown24h,
                            'type' => $countdownType,
                            'color' => $countdownClockColor,
                        ]);
                    } ?>
                </div>

                <div class="<?= $layoutStyleClass; ?>__right sp-block__animation sp-block__animation--fade-in-right">
                    <div class="prediction__field prediction__field--optin">
                        <div class="prediction__question">
                            <h3><?= get_field('optin_field_lable'); ?></h3>
                        </div>

                        <?php
                        if (!empty($dataFieldLable)) {
                            get_template_part('components/form/input', null, [
                                'label' => $dataFieldLable,
                                'name' => 'data',
                                'value' => '',
                                'type' => 'text',
                            ]);
                        } ?>

                        <?php get_template_part('components/form/input', null, [
                            'label' => get_field('optin_email_lable'),
                            'name' => 'email',
                            'value' => '',
                            'type' => 'email',
                            'autocomplete' => 'off',
                        ]); ?>

                        <?php get_template_part('components/form/checkbox', null, [
                            'label' => get_field('optin_terms_and_conditions'),
                            'class' => 'prediction__terms',
                            'name' => 'terms',
                            'value' => "true",
                        ]); ?>
                    </div>

                    <?php ob_start(); ?>
                    <?php
                    $buttonClass = getClass([
                        'btn',
                        [
                            'condition' => !empty($content['button_color']),
                            'name'      => 'btn--' . $content['button_color'],
                        ],
                        [
                            'condition' => !empty($content['button_size']),
                            'name'      => 'btn--' . $content['button_size'],
                        ],
                        [
                            'condition' => !empty($content['button_animation']),
                            'name'      => 'btn--animation-' . $content['button_animation'],
                        ],
                        [
                            'condition' => !empty($content['button_tracking']),
                            'name'      => 'js-' . $content['button_tracking'],
                            'else-name' => 'js-button-claim',
                        ]
                    ]);
                    $optinButtonClass = $buttonClass . ' btn--outlined';
                    ?>
                    <input class="<?= $optinButtonClass ?>" form="<?= $block['id']; ?>" type="submit" value="<?= get_field('optin_button_lable'); ?>">
                    <a href="<?= $content['button_url']; ?>" class="<?= $buttonClass; ?>">
                        <?= $content['button_text']; ?>
                    </a>
                    <?php
					$stickyButtonElement = ob_get_clean();
					get_template_part('campaigns/parts/cta-button-sticky', null, [
						'button' => $stickyButtonElement,
						'sticky' => empty($content['not_sticky_mobile_button']),
						'split' => true,
					]);
					?>

                    <?php get_template_part('components/terms-and-conditions', null, ['place' => 'below_cta']); ?>
                </div>
            </div>
            <?php get_template_part('components/recaptcha', null, ['action' => 'block_optin']); ?>
            </form>

            <?php get_template_part('components/terms-and-conditions', null, array_merge($termsAndConditions, ['place' => 'below_content'])); ?>
        </div>
    </section>
<?php
endif;
