<?php
function array_clear($array) {
    if (is_array($array)) {
        $array = array_map('array_clear', $array);
        return array_filter($array);
    }
    return $array;
}

function all_empty(...$variables) {
    return empty(array_filter($variables));
}

function at_least_one_has_value(...$variables) {
    return !all_empty($variables);
}

function all_have_values(...$variables) {
    return count($variables) == count(array_filter($variables));
}

function array_keys_have_values($array = [], $keys = []) {
    if (!all_have_values($array, $keys)) return false;
    foreach($keys as $key) {
        $result[$key] = $array[$key] ?: '';
    }
    return all_have_values($result);
}