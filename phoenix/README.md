# Phoenix WordPress Theme Documentation

## Overview
Phoenix is a multi-brand WordPress theme designed for casino and gaming platforms. It includes custom post types for campaigns, games, and responsible gaming, along with extensive use of Advanced Custom Fields (ACF) for dynamic content management. The theme uses Composer for PHP dependencies and includes various services for API integrations, performance optimizations, and feature toggling.

**Theme Version:** 2.9.16.18.2
**Author:** Phoenix Team
**License:** MIT License

## Directory Structure
```
phoenix/
├── acf-json/                  # ACF field group JSON files for custom fields
├── admin/                     # Admin-specific functionality
│   ├── functions-admin.php    # Core admin initialization
│   ├── functions/             # Admin utility functions
│   │   ├── brand-settings-pages/ # Brand configuration pages
│   │   ├── toolkit/           # Development and maintenance tools
│   │   ├── auto-purge-cache.php # Automatic cache purging
│   │   ├── clean-menu.php     # Admin menu cleanup
│   │   ├── topbar-menu.php    # Admin toolbar customization
│   │   └── ...                # Other admin utilities
│   ├── shortcodes/            # Admin shortcodes
│   ├── styles/                # Admin CSS (SCSS compiled)
│   ├── icons/                 # Admin icons
│   └── scripts/               # Admin JavaScript
├── app/                       # Frontend assets and templates
│   ├── images/                # Sourced images
│   ├── scripts/               # Frontend JavaScript
│   └── styles/                # SCSS/CSS files organized by feature
│       ├── global/            # Global styles and CSS variables
│       ├── colors/            # Color schemes and brand theming
│       ├── components/        # Component-specific styles
│       ├── mixins/            # SCSS mixins and utilities
│       ├── animations/        # CSS animations
│       ├── fonts/             # Font definitions
│       ├── tools/             # Utility styles
│       ├── vendor/            # Third-party styles
│       ├── blog/              # Blog-specific styles
│       ├── responsible-gaming/ # Responsible gaming styles
│       ├── start-page/        # Homepage styles
│       └── page/              # General page styles
├── assets/                    # Vendor assets and utilities
├── campaigns/                 # Campaign templates and logic
│   ├── acq/                   # Acquisition campaigns
│   │   ├── functions.php      # Acquisition post type registration
│   │   ├── offer-cards/       # Offer card templates
│   │   ├── info-page/         # Information page templates
│   │   └── ...                # Other acquisition templates
│   ├── dynamic/               # Dynamic campaigns
│   │   ├── functions.php      # Dynamic post type registration
│   │   └── ...                # Dynamic campaign templates
│   ├── crm/                   # CRM campaigns
│   │   ├── functions.php      # CRM post type registration
│   │   └── ...                # CRM campaign templates
│   └── parts/                 # Shared campaign components
├── components/                # Reusable template components
│   ├── footer-layout.php      # Footer structure
│   ├── overlay.php            # Overlay components
│   ├── header/                # Header components
│   │   ├── compliance-header-agco.php # AGCO compliance header
│   │   ├── compliance-header-gga.php  # GGA compliance header
│   │   └── ...                # Other header components
│   └── footer/                # Footer components
├── dist/                      # Compiled assets (CSS/JS)
│   ├── main.js                # Main frontend JavaScript bundle
│   ├── admin.min.js           # Admin JavaScript bundle
│   ├── admin.min.css          # Admin CSS bundle
│   └── [brand].css            # Brand-specific CSS files
├── functions/                 # Core theme functionality
│   ├── acf-config.php         # ACF configuration and JSON handling
│   ├── enqueue-scripts.php    # Asset enqueuing logic
│   ├── config.php             # Brand configuration and feature toggling
│   ├── global.php             # Global utility functions
│   ├── get-field-tweaked.php  # Enhanced ACF field retrieval
│   ├── acf-backwards-compatibility/ # ACF backwards compatibility
│   ├── performance.php        # Performance optimizations
│   ├── utilities.php          # General utility functions
│   ├── auto-enable-plugins.php # Plugin failsafe mechanism
│   ├── required-plugins.php   # Required plugin definitions
│   └── ...                    # Various utility functions
├── games/                     # Game-specific post types
│   ├── quiz/                  # Quiz game implementation
│   │   ├── functions.php      # Quiz post type and database
│   │   ├── downloadReport.php # Quiz report generation
│   │   └── ...                # Quiz templates
│   ├── letter-game/           # Letter game implementation
│   │   ├── functions.php      # Letter game post type and database
│   │   ├── downloadReport.php # Letter game report generation
│   │   └── ...                # Letter game templates
│   └── ...                    # Other game types
├── guides/                    # Guide templates and functionality
├── services/                  # Business logic and API integrations
│   ├── config.json            # Main configuration file
│   ├── config.php             # Configuration loader
│   ├── loader.php             # Service loader
│   ├── casino-games/          # Casino game services
│   │   ├── CasinoGamesAPI.php # Casino games API integration
│   │   ├── CasinoGame.php     # Casino game model
│   │   ├── functions.php      # Casino games functionality
│   │   └── ...                # Other casino game services
│   ├── player/                # Player-related logic
│   │   ├── PlayerAPI.php      # Player API integration
│   │   ├── player_service.php # Player service initialization
│   │   └── ...                # Other player services
│   ├── sportsbook/            # Sportsbook integrations
│   │   ├── loader.php         # Sportsbook provider loader
│   │   ├── providers/         # Different sportsbook providers
│   │   └── ...                # Sportsbook utilities
│   ├── maintenance/           # Maintenance utilities
│   │   ├── SlackNotifications.php # Error reporting to Slack
│   │   └── ...                # Other maintenance tools
│   ├── utility/               # Utility services
│   │   ├── Api.php            # Generic API handler
│   │   ├── SiteTypeHandler.php # Site type management
│   │   └── ...                # Other utilities
│   └── other/                 # Miscellaneous services
│       ├── BrandAPI.php       # Brand-specific API
│       ├── GeoIPBlockerAPI.php # Geo-blocking functionality
│       └── ...                # Other services
├── shortcodes/                # Custom shortcodes for content insertion
│   ├── button.php             # Button shortcode
│   ├── card.php               # Card shortcode
│   ├── cta.php                # Call-to-action shortcode
│   ├── dynamic-content.php    # Dynamic content shortcode
│   ├── player-api.php         # Player API shortcode
│   ├── get-jackpot.php        # Jackpot display shortcode
│   └── ...                    # Other shortcodes
├── start-page/                # Dynamic homepage templates
│   ├── functions.php          # Start page functionality
│   ├── dynamic/               # Dynamic blocks for homepage
│   └── pnp/                   # PNP-specific homepage components
├── template-parts/            # Reusable template partials
├── vendor/                    # Composer dependencies
│   ├── advanced-custom-fields-pro/ # ACF Pro plugin
│   ├── scotteh/php-dom-wrapper/    # DOM manipulation library
│   ├── phpoffice/phpspreadsheet/   # Excel/spreadsheet handling
│   └── ...                    # Other vendor packages
├── blog/                      # Blog functionality
│   ├── functions.php          # Blog post type and features
│   └── layout.php             # Blog layout template
├── responsible-gaming/        # Responsible gaming functionality
├── seo-page/                  # SEO page functionality
├── ad-banner/                 # Ad banner functionality
├── help-page/                 # Help page functionality
├── global-variables.php       # Global constants and variables
├── functions.php              # Main theme functions file
├── style.css                  # Theme stylesheet header
├── index.php                  # Main template file
├── header.php                 # Header template
├── footer.php                 # Footer template
├── single-[post-type].php     # Single post templates
├── composer.json              # Composer configuration
└── ...                        # Other WordPress theme files
```

## Key Features

### 1. Feature Toggling System
The theme uses a sophisticated feature toggling system controlled by `services/config.json`:

- **Configuration-driven**: Features are enabled/disabled via JSON configuration
- **Hierarchical structure**: Supports nested feature configurations (e.g., `campaigns/acq/jackpot-games`)
- **Environment-aware**: Different configurations for production, staging, and development
- **Runtime checking**: Uses `isFeatureActive($featureName)` function to conditionally load features

**Supported Features:**
- Campaign types: `campaigns/acq`, `campaigns/dynamic`, `campaigns/crm`
- Game post types: `games/quiz`, `games/letter-game`
- API integrations: `api/sportsbook`, `api/jackpots`, `api/casino-games`
- Content types: `blog`, `responsible-gaming`, `help-page`, `ad-banner`
- Start page variants: `start-page/normal`, `start-page/pnp`, `start-page/dynamic`
- Shortcodes: `shortcodes/cta`

### 2. Advanced Custom Fields (ACF) Integration
- **Bundled ACF Pro**: Includes ACF Pro plugin in `vendor/advanced-custom-fields-pro/`
- **JSON field groups**: Field configurations stored in `acf-json/` directory for version control
- **Child theme support**: Loads field groups from both parent and child themes
- **Backwards compatibility**: `acf-backwards-compatibility/` handles field migration
- **Enhanced field retrieval**: `get_field_tweaked()` function with multisite and multilingual support
- **Performance optimization**: Bulk loads options fields to reduce database queries

### 3. Multi-Brand & Multi-Language Support
- **Brand switching**: Dynamic theme switching in development environments
- **Language-specific menus**: Registers menus per language via `AUTHORITIES[CURRENT_BRAND]`
- **Multilingual ACF fields**: Supports language-specific field variations
- **Brand configuration**: Each brand has its own `config.json` with specific settings
- **Environment detection**: Different behavior for DEV, STAGING, LOCAL, and PRODUCTION

### 4. Campaign Management System
- **Multiple campaign types**:
  - **Acquisition (CAM)**: Customer acquisition campaigns with templates like offer-cards, info-pages
  - **Dynamic**: Dynamic content campaigns with flexible layouts
  - **CRM**: Customer retention campaigns with interactive elements
- **Template taxonomy**: Campaigns categorized by template types
- **Conditional loading**: Campaign features loaded based on configuration
- **Shared components**: Reusable campaign parts in `campaigns/parts/`

### 5. Gaming Platform Integration
- **Casino Games**: Integration with casino game APIs, jackpot displays, game management
- **Interactive Games**: Quiz and letter games with database tracking and reporting
- **Sportsbook Integration**: Multiple sportsbook providers (Arland, TGLab, SBTech)
- **Player Management**: Player authentication, balance tracking, session management
- **Jackpot APIs**: Real-time jackpot data from multiple providers (RedTiger, PlaynGO, Evolution, NetEnt)

### 6. Performance & Optimization
- **Asset optimization**: Compiled CSS/JS with versioning and cache busting
- **Selective loading**: Features and assets loaded only when needed
- **Database optimization**: Bulk queries for ACF options, transient caching
- **Cache management**: Automatic cache purging on version changes
- **CDN integration**: External jQuery, font preloading, DNS prefetching

### 7. Developer Tools & Maintenance
- **Phoenix Toolkit**: Admin toolbar with development utilities
- **Debug mode**: Per-user debug mode with Query Monitor integration
- **User simulation**: Simulate different user states in development
- **Time simulation**: Test time-sensitive features
- **Site type simulation**: Test different site configurations
- **Cache management**: Clear various cache types
- **Database cleanup**: Clean deprecated data and expired content
- **Error reporting**: Automatic Slack notifications for PHP errors

### 8. Security & Compliance
- **Plugin failsafe**: Automatically re-enables critical security plugins
- **Compliance headers**: AGCO and GGA compliance headers for regulated markets
- **Geo-blocking**: IP-based access restrictions
- **Session management**: Player session tracking and limits
- **Responsible gaming**: Built-in responsible gaming tools and limits

## Architecture & Design Patterns

### Configuration Management
```php
// Feature checking
if (isFeatureActive('campaigns/acq/jackpot-games')) {
    // Load jackpot games functionality
}

// Brand URL generation
$brandUrl = brandUrl(); // Returns environment-appropriate URL

// Enhanced field retrieval
$field = get_field_tweaked('field_name', 'option'); // Optimized ACF field access
```

### Service Layer Architecture
- **Service loader**: `services/loader.php` initializes all services
- **API abstraction**: Generic `Api.php` class for external API calls
- **Player service**: Centralized player management with `player()` global function
- **Casino games**: Object-oriented casino game management
- **Maintenance services**: Error handling, notifications, cleanup utilities

### Template Hierarchy
- **Component-based**: Reusable components in `components/` directory
- **Campaign templates**: Specialized templates for different campaign types
- **Template parts**: Shared template partials for common elements
- **Custom post types**: Dedicated templates for each post type

## Asset Management

### SCSS Architecture
```scss
// Main SCSS files for different contexts
app/styles/
├── main.scss           // Main frontend styles
├── blog.scss           // Blog-specific styles
├── page.scss           // General page styles
├── responsible-gaming.scss // Responsible gaming styles
└── admin/styles/main.scss  // Admin styles
```

### Build Process
- **Compiled assets**: SCSS compiled to CSS in `dist/` directory
- **JavaScript bundling**: Frontend and admin JS bundled separately
- **Version control**: File modification time used for cache busting
- **Brand-specific builds**: Different CSS files per brand theme

### Asset Enqueuing
- **Conditional loading**: Assets loaded based on page type and features
- **External dependencies**: jQuery from Google CDN, font preloading
- **Admin assets**: Separate admin CSS/JS with ACF integration
- **Performance optimization**: Gutenberg blocks CSS removed from non-page contexts

## Database Schema

### Custom Tables
- **Quiz tracking**: `comeon_quiz` table for quiz game data
- **Letter game tracking**: `comeon_game` table for letter game data
- **Player data**: Integrated with external player API
- **Campaign data**: Stored as WordPress posts with ACF fields

### Data Flow
1. **Configuration**: JSON config loaded into global variables
2. **Feature detection**: `isFeatureActive()` checks configuration
3. **Service initialization**: Services loaded based on active features
4. **Template rendering**: Templates loaded with appropriate data
5. **Asset delivery**: Optimized assets served with proper caching

## API Integrations

### External APIs
- **Player API**: Authentication, balance, session management
- **Casino Games API**: Game data, jackpots, availability
- **Sportsbook APIs**: Odds, matches, betting data
- **Brand API**: Footer links, brand-specific content
- **Geo-blocking API**: IP-based access control

### Internal APIs
- **WordPress REST API**: Custom endpoints for theme functionality
- **ACF REST API**: Field data exposure for frontend consumption
- **Custom endpoints**: Campaign data, player information, game statistics

## Security Features

### Plugin Management
- **Required plugins**: Automatic installation and activation
- **Failsafe mechanism**: Re-enables critical plugins if disabled too long
- **Security monitoring**: WP Cerber integration with automatic re-activation

### Error Handling
- **Slack notifications**: PHP errors automatically reported to Slack
- **Error throttling**: Prevents spam notifications for repeated errors
- **Environment awareness**: Different error handling for different environments
- **Debug mode**: Enhanced error reporting for developers

## Compliance & Regulation

### Regulatory Support
- **AGCO compliance**: Ontario gaming commission requirements
- **GGA compliance**: German gaming authority requirements
- **Session tracking**: Player session time and spending limits
- **Responsible gaming**: Built-in tools and restrictions
- **Panic button**: Emergency account suspension feature

### Data Protection
- **Player data**: Secure handling of sensitive player information
- **Session management**: Proper session handling and cleanup
- **Audit trails**: Comprehensive logging for regulatory compliance
- **Data retention**: Configurable data retention policies

## Configuration Reference

### Main Configuration (`services/config.json`)
```json
{
    "apiBase": {
        "production": "https://www.comeon.com",
        "dev": "https://mobile27-comeon.cleverdolphin.se",
        "staging": "https://mobile27-comeon.cleverdolphin.se"
    },
    "GTM_ID": {
        "promo": "GTM-TSZKWSZ",
        "rg": "GTM-TSZKWSZ",
        "go": "GTM-TSZKWSZ",
        "faq": "GTM-TSZKWSZ",
        "seo": "GTM-TSZKWSZ",
        "blog": "GTM-TSZKWSZ"
    },
    "configArray": {
        "start-page": {
            "normal": 0,
            "pnp": 0,
            "dynamic": 1
        },
        "campaigns": {
            "acq": 1,
            "dynamic": 1,
            "crm": {
                "beat-the-expert": 1,
                "left-and-right": 1,
                "interactive": 1,
                "wheel": 1,
                "offer-of-the-day": 1,
                "calendar-offers": 1,
                "money-drop": 1
            }
        },
        "games": {
            "quiz": 1,
            "letter-game": 1
        },
        "api": {
            "sportsbook": {
                "arland": 1,
                "tglab": 0,
                "sbtech": 0
            },
            "jackpots": {
                "redtiger": 1,
                "playngo": 1,
                "evolution": 1,
                "netent": 1
            }
        }
    }
}
```

### Environment Constants
- `DEV_ENV`: Development environment flag
- `STAGING_ENV`: Staging environment flag
- `LOCAL_ENV`: Local development flag
- `PRODUCTION_ENV`: Production environment flag
- `DEBUG_MODE`: Per-user debug mode
- `SIMULATED_USER`: User simulation mode
- `CURRENT_BRAND`: Active brand identifier
- `CURRENT_REGION`: Current region/locale
- `CURRENT_CURRENCY`: Active currency

### Post Type Constants
- `CAMPAIGN_SLUG`: Main campaign post type
- `ACQUISITION_SLUG`: Acquisition campaign post type
- `DYNAMIC_SLUG`: Dynamic campaign post type
- `QUIZ_SLUG`: Quiz game post type
- `LETTER_GAME_SLUG`: Letter game post type
- `CASINO_GAMES_SLUG`: Casino games post type
- `RESPONSIBLE_GAMING_SLUG`: Responsible gaming post type
- `SEO_PAGE_SLUG`: SEO page post type

## Composer Dependencies

### Required Packages
```json
{
    "require": {
        "scotteh/php-dom-wrapper": "^3.0",
        "phpoffice/phpspreadsheet": "^3.5"
    }
}
```

### Package Usage
- **php-dom-wrapper**: DOM manipulation for content processing
- **phpspreadsheet**: Excel report generation for games and campaigns
- **ACF Pro**: Advanced Custom Fields Pro (bundled in vendor/)

## WordPress Integration

### Theme Support
- **Post thumbnails**: Featured image support
- **Title tag**: Dynamic title generation
- **HTML5**: Modern markup support
- **Custom menus**: Multiple menu locations
- **Shortcodes**: Enhanced shortcode support in ACF fields

### Custom Post Types
- **Campaigns**: Acquisition, Dynamic, CRM campaigns
- **Games**: Quiz, Letter Game, Casino Games
- **Content**: Blog, Guides, SEO Pages, Responsible Gaming
- **Taxonomies**: Template types, tags, categories

### Hooks & Filters
- **Custom template hierarchy**: Modified post type URL structure
- **ACF field filters**: Enhanced field processing
- **Asset optimization**: Selective script/style loading
- **Performance hooks**: Caching and optimization

## Development Workflow

### Local Development
1. **Theme switching**: Use Phoenix Toolkit to switch between brands
2. **Debug mode**: Enable per-user debugging
3. **User simulation**: Test different user states
4. **Feature toggling**: Enable/disable features via config
5. **Cache management**: Clear caches during development

### Deployment Process
1. **Asset compilation**: SCSS/JS compilation
2. **Version bumping**: Update RELEASE_VERSION constant
3. **Cache purging**: Automatic cache clearing on version change
4. **Feature validation**: Ensure proper feature configuration
5. **Database migrations**: Handle schema changes

### Testing Tools
- **Phoenix Toolkit**: Admin toolbar development utilities
- **Query Monitor**: Database query analysis
- **Debug mode**: Enhanced error reporting
- **Transient manager**: Cache inspection and management
- **Config validation**: Feature configuration testing

## Troubleshooting

### Common Issues
1. **Missing styles**: Check if SCSS compilation is working
2. **Feature not loading**: Verify `isFeatureActive()` configuration
3. **ACF fields missing**: Check `acf-json/` directory and field groups
4. **API errors**: Verify external API configurations and keys
5. **Cache issues**: Use Phoenix Toolkit cache clearing tools

### Debug Information
- **Debug mode**: Enable via Phoenix Toolkit
- **Query Monitor**: Install for database query analysis
- **Slack notifications**: Monitor error channel for issues
- **Transients**: Check cached data via Transients Manager
- **Config validation**: Use Testing Tools page for configuration review

### Performance Optimization
1. **Feature toggling**: Disable unused features
2. **Asset optimization**: Minimize loaded CSS/JS
3. **Database queries**: Use bulk loading for ACF options
4. **Caching**: Implement proper transient usage
5. **CDN usage**: Leverage external resources for common libraries

## Maintenance & Updates

### Regular Maintenance
- **Plugin updates**: Keep security plugins current
- **Database cleanup**: Remove deprecated data
- **Cache optimization**: Monitor and optimize caching strategies
- **Error monitoring**: Review Slack error notifications
- **Performance monitoring**: Track page load times and database queries

### Version Management
- **Release versioning**: Update RELEASE_VERSION for deployments
- **Feature flags**: Use configuration to manage feature rollouts
- **Database migrations**: Handle schema changes properly
- **Backwards compatibility**: Maintain ACF field compatibility
- **Asset versioning**: Ensure proper cache busting

This documentation provides a comprehensive overview of the Phoenix WordPress theme architecture, features, and development practices. For specific implementation details, refer to the individual files and their inline documentation.
