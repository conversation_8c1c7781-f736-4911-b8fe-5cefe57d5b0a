<?php
if (!is_array($block)) $block = []; // Fallback for array_merge below
$block = array_merge([
    'id' => '',
    'className' => '',
    'anchor' => '',
], $block);

$blockContent = get_field('block_content');
$contentBackgroundColor = get_field('content_background_color');

$products = get_field('block_products');
if (!empty($products) && is_array($products)) {
    $products = array_map(function ($product) {
        if (is_array($product) && !all_have_values(...$product)) {
            return null;
        }
        return $product;
    }, $products);

    $products = array_filter($products);
}
$termsAndConditions = get_field('terms_and_conditions_group');
$mobileLayout = get_field('mobile_layout');
$toogleLayout = ($mobileLayout == 'toggle');
$desktopLayout = get_field('desktop_layout');
$splitLayout = ($desktopLayout == 'simple');

$background = get_field('block_background');
addBackgroundInlineStyle('.sp-block--' . $block['id'] . ' .background', $background);

if( !empty($contentBackgroundColor) ) {
    addBackgroundInlineStyle(
        '.sp-block-2products__content-bg',
        [
            'background_type' => true, // false => image,  true => color
            'background_color' => $contentBackgroundColor
        ]
    );
}

get_template_part('dynamic/parts/preview');

$spBlockClass = getClass([
    'sp-block',
    'sp-block--' . $block['id'],
    $block['className'],
    "sp-block-2products sp-block-2products--mobile-{$mobileLayout} sp-block-2products--desktop-{$desktopLayout}"
]);

if (at_least_one_has_value($products, $blockContent)) : ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spBlockClass; ?>">
        <?php if ($splitLayout) : ?>
            <?php foreach ($products as $product) : ?>
                <div class="sp-block-2products__background__split" style="background-image: url(<?= ($product['product_images'][0]['image'] ?? '') ?>);"></div>
            <?php endforeach; ?>
        <?php endif; ?>
        <div class="sp-block-2products__background background content-stretchable block-stretchable">
            <?php get_template_part('campaigns/parts/background-video', null, $background); ?>
            <?php get_template_part('campaigns/parts/background-lottie', null, $background); ?>

            <div class="container sp-block__container">
                <?php if ( !empty($blockContent['heading_image']) ) : ?>
                    <div class="sp-block__heading-image wrapper sp-block__animation sp-block__animation--fade-in-center">
                        <img src="<?= $blockContent['heading_image'];?>" alt="<?= wp_strip_all_tags($blockContent['title']); ?>">
                    </div>
                <?php endif; ?>

                <?php if (!empty($blockContent['title']) || !empty($blockContent['subtitle'])) : ?>
                    <?php
                    $contentClass = getClass([
                        'wrapper sp-block__animation sp-block__animation--fade-in-center',
                        [
                            'condition' => (!empty($contentBackgroundColor)),
                            'name' => 'sp-block-2products__content-bg',
                        ]
                    ]);
                    ?>
                    <div class="<?= $contentClass; ?>">
                    <?php if (!empty($blockContent['title'])) : ?>
                        <h2 class="sp-block__title<?= (!empty($blockContent['exclude_jumplinks']) ?? ' no-jump-link') ?>"><?= $blockContent['title']; ?></h2>
                    <?php endif; ?>
                    <?php if (!empty($blockContent['subtitle'])) : ?>
                        <h3 class="sp-block__subtitle"><?= $blockContent['subtitle']; ?></h3>
                    <?php endif; ?>

                    <?php if (!empty($blockContent['description'])) : ?>
                    <div class="sp-block__description wrapper sp-block__animation sp-block__animation--fade-in-center">
                        <?php get_template_part('components/read-more', null, $blockContent['description']); ?>
                    </div>
                    <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="sp-block__animation sp-block__animation--fade-in-center">
                    <?php if (!empty($products) && is_array($products)) : ?>
                        <?php if ($toogleLayout) : ?>
                            <div class="sp-block-2products__toggle js-2products-toggle">
                                <?php foreach ($products as $key => $product) : ?>
                                    <div class="sp-block-2products__option js-2products-toggle-option" for="two-products-toogle-<?= $key; ?>"><?= $product['product_title']; ?></div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <div class="sp-block-2products__items">
                            <?php foreach ($products as $key => $product) : ?>
                                <?php
                                $blockColumnButton = $product['product_button'];
                                $blockColumnClass = getClass([
                                    'sp-block-2products__item',
                                    [
                                        'condition' => $toogleLayout,
                                        'name' => 'js-2products-toogle-content',
                                    ],
                                    [
                                        'condition' => $toogleLayout & $key == 0,
                                        'name' => 'visible',
                                    ],
                                ]);
                                ?>
                                <div class=" <?= $blockColumnClass; ?>" data-content="two-products-toogle-<?= $key; ?>">
                                    <h3 class="sp-block-2products__item-title"><?= $product['product_title']; ?></h3>
                                    <div class="sp-block-2products__images">
                                        <?php foreach ($product['product_images'] as $image) : ?>
                                            <?php if (!empty($image['image'])) : ?>
                                                <a href="<?= $blockColumnButton['button_url']; ?>" class="sp-block-2products__image <?= (!empty($blockColumnButton['button_tracking']) ? 'js-' . $blockColumnButton['button_tracking'] : 'js-button-claim'); ?>">
                                                    <img src="<?= $image['image']; ?>" alt="<?= wp_strip_all_tags($product['product_title']); ?>" />
                                                </a>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>

                                    <?php ob_start(); ?>
                                    <?php
                                    $blockColumnButtonClass = getClass([
                                        'btn',
                                        (!empty($blockColumnButton['button_color']) ? 'btn--' . $blockColumnButton['button_color'] : ''),
                                        (!empty($blockColumnButton['button_size']) ? 'btn--' . $blockColumnButton['button_size'] : ''),
                                        (!empty($blockColumnButton['button_animation']) ? 'btn--animation-' . $blockColumnButton['button_animation'] : ''),
                                        (!empty($blockColumnButton['button_tracking']) ? 'js-' . $blockColumnButton['button_tracking'] : 'js-button-claim'),
                                    ]);
                                    ?>
                                    <a href="<?= $blockColumnButton['button_url']; ?>" class="<?= $blockColumnButtonClass; ?>">
                                        <?= $blockColumnButton['button_text']; ?>
                                    </a>
                                    <?php
                                    $stickyButtonElement = ob_get_clean();
                                    get_template_part('campaigns/parts/cta-button-sticky', null, [
                                        'button' => $stickyButtonElement,
                                        'sticky' => $toogleLayout,
                                        'content' => 'two-products-toogle-' . $key,
                                        'visible' => !$key,
                                    ]);
                                    ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php get_template_part('components/terms-and-conditions', null, $termsAndConditions); ?>
            </div>
        </div>
    </section>
<?php
endif;
