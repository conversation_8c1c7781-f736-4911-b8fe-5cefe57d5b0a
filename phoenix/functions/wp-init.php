<?php
// Theme support
add_theme_support('title-tag');
add_theme_support('post-thumbnails');

// Display Shortcodes in acf fields
add_filter('acf/format_value/type=text', 'do_shortcode');
add_filter('acf/format_value/type=textarea', 'do_shortcode');
add_filter('acf/format_value/type=wysiwyg', 'do_shortcode');

// Remove shortlink from head
add_filter('after_setup_theme', 'remove_shortlink');
function remove_shortlink() {
    remove_action('wp_head', 'wp_shortlink_wp_head', 10);
    remove_action('template_redirect', 'wp_shortlink_header', 11);
}

// Embedded element size
add_filter('embed_defaults', 'crunchify_embed_defaults');
function crunchify_embed_defaults($embed_size) {
    $embed_size['width'] = 610;
    $embed_size['height'] = '100%';
    return $embed_size;
}

// Responsive container
add_filter('embed_oembed_html', 'wrap_embed_with_div', 10, 3);
function wrap_embed_with_div($html, $url, $attr) {
    return '<div class="responsive-container">' . $html . '</div>';
}

// Add html5 support to fix w3 markup validation issues
add_action('after_setup_theme','add_html5_support');
function add_html5_support() {
    add_theme_support( 'html5', [ 'script', 'style' ] );
}

// Remove RSD since no-one is using Really Simple Discovery (RSD) for publishing/reading content anymore
remove_action( 'wp_head', 'rsd_link' );

// No-one is using Windows Live Writer
remove_action( 'wp_head', 'wlwmanifest_link' );

// We dont want category, tag feed urls (these called extra feed links) to appear in page source for SEO, this doesnt affect main blog feed url
remove_action( 'wp_head', 'feed_links_extra', 3 );

// Hide wp version for black-hats
remove_action( 'wp_head', 'wp_generator' );

// General
add_action('pre_get_posts', 'render_page_without_post_type_slug');

// Remove type from permalink
function render_page_without_post_type_slug($query)
{
    if (!$query->is_main_query())
        return;

    if (2 != count($query->query) || !isset($query->query['page']))
        return;

    if (!empty($query->query['name'])) {

        $query->set('post_type', ['post', CAMPAIGN_SLUG, ACQUISITION_SLUG, DYNAMIC_SLUG, RESPONSIBLE_GAMING_SLUG, SEO_PAGE_SLUG, 'page']);
    }
}

// Disable WP API for non-logged-in users
add_filter('rest_authentication_errors', function ($result) {
    if (!empty($result)) {
        return $result;
    }
    if (!is_user_logged_in()) {
        return new WP_Error('rest_not_logged_in', 'You are not currently logged in.', ['status' => 401]);
    }
    return $result;
});
