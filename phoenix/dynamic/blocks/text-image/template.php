<?php
global $blockContent, $blockButton;
getBlockCommonFields();
$block = array_merge([
    'id'     => '',
    'className' => '',
    'anchor' => '',
], $block);
$type = get_field('block_type');
$fontColor = 'normal-font-color';
if (get_field('invert_text_color')) {
    $fontColor = 'invert-font-color';
}
$background = get_field('block_background');
addBackgroundInlineStyle('.sp-block--' . $block['id'], $background);

$column_order = get_field('block_column_order');
$block_image  = get_field('block_image');
$block_layout = get_field('block_layout');

get_template_part('dynamic/parts/preview');

$spBlockClass = getClass([
    'sp-block',
    'sp-block--' . $type,
    'sp-block--' . $fontColor,
    'sp-block--' . $block['id'],
    $block['className'],
    'block-stretchable',
    'sp-block-text-image sp-block-text-image__container',
    'sp-block-text-image--order-' . $column_order,
    (!empty($block['data']['minimize_top_padding']) ? ' sp-block--minimize-padding-top' : ''),
    (!empty($block['data']['minimize_bottom_padding']) ? ' sp-block--minimize-padding-bottom' : ''),
]);

if (at_least_one_has_value($block_image, $blockContent, array_keys_have_values($blockButton, ['button_text', 'button_url']))) : ?>
    <section id="<?= $block['anchor']; ?>" class="<?= $spBlockClass; ?> ">
        <?php get_template_part('campaigns/parts/background-video', null, $background); ?>
        <?php get_template_part('campaigns/parts/background-lottie', null, $background); ?>
        <div class="container sp-block__container">
            <?php
            $contentContainerClass = getClass([
                'wrapper wrapper--md-80',
                [
                    'condition' => !empty($block_layout['layout_settings']['full_width']),
                    'name'      => 'wrapper--wider'
                ],
                [
                    'condition' => empty($block['data']['minimize_top_padding']) && empty($block['data']['minimize_bottom_padding']),
                    'name'      => 'content-margin'
                ],
                [
                    'condition' => empty($block['data']['minimize_top_padding']) && !empty($block['data']['minimize_bottom_padding']),
                    'name'      => 'content-margin-top'
                ],
                [
                    'condition' => empty($block['data']['minimize_bottom_padding']) && !empty($block['data']['minimize_top_padding']),
                    'name'      => 'content-margin-bottom'
                ]
            ]);
            ?>
            <div class="<?= $contentContainerClass; ?>">
                <?php
                $rowClass = getClass([
                    'row',
                    [
                        'condition' => !empty($block_layout['layout_settings']['columns_size']),
                        'name'      => 'row--' . $block_layout['layout_settings']['columns_size'],
                    ]
                ]);
                ?>
                <div class="<?= $rowClass; ?>">
                    <!-- content -->
                    <div class="column column--left">
                        <div class="sp-block__animation sp-block__animation--fade-in-top">
                            <?php get_template_part('dynamic/parts/block', 'content'); ?>
                        </div>
                        <div class=" sp-block__animation sp-block__animation--fade-in-bottom">
                            <?php get_template_part('dynamic/parts/block', 'cta'); ?>
                        </div>
                    </div>
                    <div class="column column--right sp-block__animation sp-block__animation--fade-in-<?= $column_order == 'text-and-image' ? 'right' : 'left'; ?>">
                        <?php if (!empty($block_image)) : ?>
                            <img src="<?= $block_image; ?>" width="360" alt="<?= wp_strip_all_tags($blockContent['title']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php endif;
